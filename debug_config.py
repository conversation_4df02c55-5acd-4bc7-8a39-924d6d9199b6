#!/usr/bin/env python3
"""
配置调试脚本
"""

import sys
import pathlib
import toml

# 添加src目录到Python路径
sys.path.insert(0, str(pathlib.Path(__file__).parent / "src"))

from qyem_demo.config import Config

def debug_config():
    """调试配置数据结构"""
    print("开始调试配置...")
    
    # 直接加载TOML文件
    config_path = "config.toml"
    print(f"加载配置文件: {config_path}")
    
    try:
        config_data = toml.load(config_path)
        print("✅ TOML文件加载成功")
    except Exception as e:
        print(f"❌ TOML文件加载失败: {e}")
        return
    
    # 打印所有顶级键
    print("\n顶级配置键:")
    for key in config_data.keys():
        print(f"  - {key}")
    
    # 检查collection-paradigm配置
    print("\ncollection-paradigm配置:")
    if "collection-paradigm" in config_data:
        paradigm = config_data["collection-paradigm"]
        print(f"  stimulis_group: {paradigm.get('stimulis_group', 'NOT_FOUND')}")
        
        # 检查group配置 - 直接在paradigm对象中查找
        stimulis_group = paradigm.get("stimulis_group", "group-default")
        print(f"  查找的组: {stimulis_group}")
        
        # 检查paradigm字典中是否有对应的组配置
        if stimulis_group in paradigm:
            print(f"  ✅ 找到 {stimulis_group} 配置")
            group_config = paradigm[stimulis_group]
            print(f"    刺激目录: {group_config.get('stimulus_dir', 'NOT_FOUND')}")
            stimulis = group_config.get('stimulis', [])
            print(f"    刺激数量: {len(stimulis)}")
            
            # 检查刺激列表
            print(f"    前5个刺激:")
            for i, stimulus in enumerate(stimulis[:5]):
                print(f"      {i+1}. {stimulus}")
            
            # 检查是否有校准图像
            has_calibration = any("calibration" in s.lower() for s in stimulis)
            print(f"    包含校准图像: {'✅' if has_calibration else '❌'}")
            
            # 检查图像目录是否存在
            stimulus_dir = group_config.get('stimulus_dir', '')
            stimulus_path = pathlib.Path(stimulus_dir)
            if stimulus_path.exists():
                print(f"    刺激目录存在: ✅")
                image_files = list(stimulus_path.glob('*.png')) + list(stimulus_path.glob('*.jpg'))
                print(f"    目录中的图像文件数量: {len(image_files)}")
            else:
                print(f"    刺激目录存在: ❌ ({stimulus_path})")
            
        else:
            print(f"  ❌ 未找到 {stimulis_group} 配置")
            print("  可用的组:")
            for key in paradigm.keys():
                if key not in ['subscriptions', 'stimulis_group']:
                    print(f"    - {key}")
    else:
        print("  ❌ 未找到 collection-paradigm 配置")

    # 检查设备配置
    print("\n设备配置:")
    if "device" in config_data:
        device = config_data["device"]
        print(f"  设备名称: {device.get('name', 'NOT_FOUND')}")
        license_file = device.get('license_file', '')
        if license_file:
            license_path = pathlib.Path(license_file)
            if license_path.exists():
                print(f"  许可证文件: ✅ ({license_file})")
            else:
                print(f"  许可证文件: ❌ ({license_file})")
        else:
            print(f"  许可证文件: 未配置")
    
    # 检查输出目录
    print("\n输出配置:")
    if "global" in config_data:
        global_config = config_data["global"]
        output_dir = global_config.get('output_dir', './out')
        output_path = pathlib.Path(output_dir)
        if output_path.exists():
            print(f"  输出目录: ✅ ({output_dir})")
        else:
            print(f"  输出目录: ❌ ({output_dir}) - 将会自动创建")

if __name__ == "__main__":
    debug_config() 