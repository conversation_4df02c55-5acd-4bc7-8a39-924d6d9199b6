#!/usr/bin/env python3
"""
GUI启动器 - 直接启动眼动仪数据采集系统的图形界面

使用方法:
    python gui_launcher.py              # 正常模式启动GUI
    python gui_launcher.py --dry-run    # 演示模式启动GUI（无硬件连接）
    python gui_launcher.py --help       # 显示帮助信息
"""

import argparse
import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))


from qyem_demo.gui_app import GuiApp  # type: ignore  # noqa: E402
from qyem_demo.__about__ import __version__, __summary__  # type: ignore  # noqa: E402


def create_launcher_parser() -> argparse.ArgumentParser:
    """
    创建GUI启动器的命令行参数解析器

    Returns:
        参数解析器
    """
    parser = argparse.ArgumentParser(
        description=f"GUI启动器 - {__summary__} (Version {__version__})",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用说明:
  这是一个简化的GUI启动器，会直接启动图形界面。
  
示例用法:
  python gui_launcher.py              # 正常模式
  python gui_launcher.py --dry-run    # 演示模式（不连接真实眼动仪）
        """,
    )

    parser.add_argument(
        "-v", "--version",
        action="version",
        version=f"%(prog)s {__version__}"
    )

    parser.add_argument(
        "--config",
        "-c",
        type=str,
        default="config.toml",
        help="配置文件路径 (默认: config.toml)",
    )

    parser.add_argument(
        "--license", 
        "-l", 
        type=str, 
        help="许可证文件路径（可选，会自动查找licenses目录）"
    )

    parser.add_argument(
        "--dry-run", 
        action="store_true", 
        help="演示模式（不连接真实眼动仪设备）"
    )
    
    parser.add_argument(
        "--skip-calibrate",
        action="store_true",
        help="跳过眼动仪校准步骤"
    )

    return parser


def main() -> int:
    """
    GUI启动器主函数

    Returns:
        退出代码（0表示成功）
    """
    # 解析命令行参数
    parser = create_launcher_parser()
    args = parser.parse_args()

    try:
        # 打印启动信息
        print("🚀 眼动仪数据采集系统 - GUI启动器")
        print("=" * 50)
        print(f"版本: {__version__}")
        print(f"配置文件: {args.config}")
        print(f"演示模式: {'是' if args.dry_run else '否'}")
        if args.license:
            print(f"许可证: {args.license}")
        if args.skip_calibrate:
            print("跳过校准: 是")
        print("=" * 50)

        # 创建并启动GUI应用
        gui_app = GuiApp(dry_run=args.dry_run)
        exit_code: int = gui_app.run(args)
        return exit_code

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n💥 启动器发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 