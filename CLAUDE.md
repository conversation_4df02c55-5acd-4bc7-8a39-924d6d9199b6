# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

```bash
# Install dependencies
uv sync

# Activate environment
uv shell

# Run GUI application
python -m qyem_demo

# Run in demo mode (no hardware)
python -m qyem_demo --dry-run

# Run with custom config
python -m qyem_demo --config config.toml

# Apply license
python -m qyem_demo --license licenses/se_license_key.bytes --config config.toml

# Run tests
uv run pytest

# Run specific test file
uv run pytest tests/test_user_workflow.py

# Run tests with coverage
uv run pytest --cov=src/qyem_demo

# Format code
uv run black src/ tests/
uv run isort src/ tests/

# Type checking
uv run mypy src/

# Linting
uv run flake8 src/ tests/

# Quality checks and fixes
python scripts/run_tests.py --fix
python scripts/run_tests.py --quality
```

## Architecture Overview

### MVP Architecture (Current)
- **Model**: `src/qyem_demo/ui/models/` - Data structures (UserInfo, AssessmentData)
- **View**: `src/qyem_demo/ui/pages/` - UI components (HomePage, AssessmentPage)
- **Presenter**: `src/qyem_demo/ui/presenters/` - Business logic (HomePresenter, AssessmentPresenter, ExperimentPresenter)
- **Interfaces**: `src/qyem_demo/ui/interfaces/` - Contracts between layers using composition pattern

### Core System Components
- **MainWindow** (`src/qyem_demo/ui/main_window.py`): Central workflow coordinator
- **EyeTracker** (`src/qyem_demo/eyetracker.py`): Hardware interface, calibration, and license management
- **Collector** (`src/qyem_demo/collector.py`): Real-time eye-tracking data collection with thread safety
- **Paradigm** (`src/qyem_demo/paradigm.py`): Experiment flow management and stimulus presentation
- **Config** (`src/qyem_demo/config.py`): TOML-based configuration with stimulus group support
- **QualityChecker** (`src/qyem_demo/quality_checker.py`): Data validation and quality assessment

### 7-Step Workflow
1. **User Info Collection** → cache to `data/{subject_id}/`
2. **Assessment Data Collection** → cache with validation
3. **Experiment Execution** → validation with data collection control
4. **Data Quality Check** → user confirmation for invalid data
5. **Save Valid Data** → CSV + report in `out/{timestamp}_{subject_id}/`
6. **Cache Management** → clear/retain based on user choice
7. **Return to Start** → reset state and clear forms

## Development Commands

```bash
# Run specific test categories
python scripts/run_tests.py --unit
python scripts/run_tests.py --integration
python scripts/run_tests.py --workflow
python scripts/run_tests.py --performance

# Generate comprehensive report
python scripts/run_tests.py --all --coverage --report

# Build and package
uv run hatch build

# Clean and rebuild
uv run hatch clean
uv run hatch build
```

## Configuration System

### File Structure
- **Config file**: `config.toml` (TOML format, see `config.toml.example`)
- **License**: Place in `licenses/` directory (`.bytes` format)
- **Stimulus groups**: Organized in `images/{group-name}/` directories
- **Output**: Generates CSV data and reports in `out/{timestamp}_{subject_id}/`
- **Cache**: Uses `data/{subject_id}/` for session recovery
- **Calibration**: Stores calibration data in `calibration.bin`

### Stimulus Configuration
```toml
[collection-paradigm]
stimulis_group = "group-default"
subscriptions = ["gaze_data"]

[collection-paradigm.group-default]
stimulus_dir = "./images/group-default"
stimulis = [
    "image, 12s, calibration.png, true",   # type, duration, file, collect_data
    "image, 6s, stimulus.jpg, true",
    "image, 3s, rest.jpg, false",
]
```

## Testing Framework

### Test Categories
- **Unit tests**: `tests/test_core_modules.py` - Individual component testing
- **Workflow tests**: `tests/test_user_workflow.py` - 7-step process validation
- **GUI tests**: `tests/test_gui_components.py` - MVP pattern testing
- **Integration tests**: `tests/test_integration.py` - End-to-end scenarios
- **Performance tests**: `tests/test_performance.py` - Memory and speed optimization
- **Error handling**: `tests/test_error_handling.py` - Edge cases and failures
- **Data validation**: `tests/test_form_validation.py` - Input validation
- **Fixtures**: `tests/test_data_fixtures.py` - Test data generation

### Test Execution
```bash
# Run all tests with coverage
uv run pytest --cov=src/qyem_demo --cov-report=html

# Run specific test patterns
uv run pytest -k "test_user_info_validation"
uv run pytest -m "not performance"  # Exclude slow tests

# Parallel test execution
uv run pytest -n auto
```

## Key Files & Entry Points

### Application Entry
- **CLI**: `src/qyem_demo/__main__.py` - Command-line interface
- **GUI**: `gui_launcher.py` - Dedicated GUI launcher with args support
- **Module**: `src/qyem_demo/__init__.py` - Package initialization

### UI Layer
- **MainWindow**: `src/qyem_demo/ui/main_window.py` - Central coordinator
- **HomePage**: `src/qyem_demo/ui/pages/home_page.py` - User info collection
- **AssessmentPage**: `src/qyem_demo/ui/pages/assessment_page.py` - Cognitive assessment
- **PlaybackWindow**: `src/qyem_demo/ui/playback_window.py` - Experiment display

### Core Modules
- **BaseApp**: `src/qyem_demo/base_app.py` - Common application functionality
- **GUIApp**: `src/qyem_demo/gui_app.py` - Qt application setup

## Project Structure

```
src/qyem_demo/
├── __init__.py
├── __main__.py          # CLI entry point
├── base_app.py          # Base application class
├── collector.py         # Data collection engine
├── config.py            # Configuration management
├── eyetracker.py        # Eye tracker interface
├── gui_app.py           # GUI application setup
├── paradigm.py          # Experiment paradigm manager
├── quality_checker.py   # Data quality validation
└── ui/                  # MVP architecture
    ├── main_window.py   # Central coordinator
    ├── pages/           # View components
    ├── presenters/      # Business logic
    ├── models/          # Data models
    ├── interfaces/      # Contracts/interfaces
    └── playback_window.py
```

## Development Environment

### Requirements
- Python 3.10+ (specified in pyproject.toml)
- uv package manager
- PySide6 for GUI
- Optional: tobii-research for real eye tracker

### Setup
```bash
# Install uv if not available
curl -LsSf https://astral.sh/uv/install.sh | sh

# Clone and setup
git clone <repository-url>
cd qyem-demo
uv sync
uv shell
```