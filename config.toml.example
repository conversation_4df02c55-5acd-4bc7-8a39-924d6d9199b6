# 配置描述
description = "眼动仪配置示例文件"
notes = "此文件包含眼动仪的各种配置参数"

# 全局配置
[global]
output_dir = "./out"

# 设备配置
[device]
name = "IS510-100204006643"
eyetracker_address = ["tobii-prp://IS510-100204006643"]
license_file = "./licenses/se_license_key_qianya_temporary_professional_20251219.bytes"

# 采集范式
[collection-paradigm]
subscriptions = ["gaze_data"]
stimulis_group = "group-default"

# 眼动仪配置
[tracker_config]
# 眼动数据输出频率 (Hz)
gaze_output_frequency = 60.0
# 支持的频率列表
supported_frequencies = [
    30.0,
    60.0,
    120.0,
    250.0
]

# 默认刺激组配置
[collection-paradigm.group-default]
stimulus_dir = "./images/group-default"
stimulis = [
    "image, 12s, em_check_calibration.png",
    "image, 6s, em_empty_table.jpg",
    "image, 6s, em_photoframe_on_table.jpg",
]

# 0716刺激组配置
[collection-paradigm.group-0716]
stimulus_dir = "./images/group-0716"
stimulis = [
    "image, 6s, 20241015103407601388015243269.png",
    "image, 6s, 20241015104308601390228721669.png",
    "image, 6s, 20241015104345601390379507717.png",
    "image, 6s, 20241015104423601390538625029.jpg",
    "image, 6s, 20241015104455601390667767813.png",
    "image, 6s, 20241015104520601390771265541.jpg",
    "image, 6s, 20241015104539601390849540101.png",
    "image, 6s, 20241015104556601390917431301.jpg",
    "image, 6s, 20241015105029601392036188165.png",
    "image, 6s, 20241015105056601392145190917.jpg",
    "image, 6s, 20241015105256601392638328837.png",
    "image, 6s, 20241015105359601392894832645.jpg",
    "image, 6s, 20241015105534601393285496837.png",
    "image, 6s, 20241015105554601393365254149.jpg",
    "image, 6s, 20241015105607601393418772485.png",
    "image, 6s, 20241015105733601393772576773.png",
    "image, 6s, 20241015105751601393845252101.png",
    "image, 6s, 20241015105929601394249326597.jpg",
    "image, 6s, 20241015105940601394292191237.png",
    "image, 6s, 20241015105959601394371821573.jpg",
    "image, 6s, 20241015110018601394447835141.png",
    "image, 6s, 20241015110255601395090771973.jpg",
    "image, 6s, 20241015110302601395121909765.png",
    "image, 6s, 20241015110322601395201425413.jpg",
    "image, 6s, 20241015110347601395302801413.png",
    "image, 6s, 20241015110442601395527938053.jpg",
    "image, 6s, 20241015110522601395694657541.png",
    "image, 6s, 20241015110652601396061925381.jpg",
    "image, 6s, 20241015110747601396285984773.png",
    "image, 6s, 20241015110925601396690026501.png",
    "image, 6s, 20241015111059601397074046981.jpg",
    "image, 6s, 20241015111113601397129818117.png",
    "image, 6s, 20241015111136601397225340933.jpg",
    "image, 6s, 20241015111147601397269839877.png",
    "image, 6s, 20241015111208601397355933701.jpg",
    "image, 6s, 20241015111221601397409574917.png",
    "image, 6s, 20241015111324601397667663877.jpg",
    "image, 6s, 20241015111711601398595710981.png",
    "image, 6s, 20241015111726601398658764805.jpg",
    "image, 6s, 20241015111743601398727086085.png",
    "image, 6s, 20241015111757601398784360453.jpg",
    "image, 6s, 20241015111917601399113760773.png",
    "image, 6s, 20241015112012601399340535813.png",
    "image, 6s, 20241015112028601399404888069.jpg",
    "image, 6s, 20241015112038601399446822917.png",
    "image, 6s, 20241015112052601399503298565.jpg",
    "image, 6s, 20241015112135601399680208901.png",
    "image, 6s, 20241015112308601400058429445.jpg",
    "image, 6s, 20241015112344601400206102533.png",
    "image, 6s, 20241015112357601400259493893.jpg",
    "image, 6s, 20241015112415601400334016517.png",
    "image, 6s, 20241015112436601400422006789.jpg",
    "image, 6s, 20241015112454601400494161925.png",
    "image, 6s, 20241015112506601400544194565.jpg",
    "image, 6s, 20241015112532601400650178565.png",
    "image, 6s, 20241015112555601400744083461.png",
    "image, 6s, 20241015112622601400856289285.jpg",
    "image, 6s, 20241015112703601401022996485.png",
    "image, 6s, 20241015112734601401150812165.jpg",
    "image, 6s, 20241015112857601401489743877.png",
    "image, 6s, 20241015112930601401623830533.jpg",
    "image, 6s, 20241015112938601401656004613.png",
    "image, 6s, 20241015112957601401734008837.jpg",
    "image, 6s, 20241015113006601401773236229.png",
    "image, 6s, 20241015113039601401907220485.jpg",
    "image, 6s, 20241015113053601401965178885.png",
    "image, 6s, 20241015113135601402136510469.jpg",
    "image, 6s, 20241015113304601402501431301.png",
    "image, 6s, 20241015113605601403241857029.png",
    "image, 6s, 20241015113619601403300962309.jpg",
    "image, 6s, 20241015113631601403347410949.png",
    "image, 6s, 20241015113654601403444875269.jpg",
    "image, 6s, 20241015113710601403507404805.png",
    "image, 6s, 20241015113729601403586707461.jpg",
    "image, 6s, 20241015113740601403631640581.png",
    "image, 6s, 20241015113800601403714449413.jpg",
    "image, 6s, 20241015113808601403747975173.png",
    "image, 6s, 20241015113829601403832832005.jpg",
    "image, 6s, 20241015113850601403916742661.jpg",
    "image, 6s, 20241015114148601404648898565.png",
    "image, 6s, 20241015114206601404722270213.png",
    "image, 6s, 20241015114236601404843970565.jpg",
    "image, 6s, 20241015114249601404897849349.png",
    "image, 6s, 20241015114352601405154893829.jpg",
    "image, 6s, 20241015114411601405233201157.png",
    "image, 6s, 20241015114432601405318180869.jpg",
    "image, 6s, 20241015114444601405366915077.png",
    "image, 6s, 20241015114506601405457010693.jpg",
    "image, 6s, 20241015114516601405499875333.png",
    "image, 6s, 20241015114541601405599993861.jpg",
    "image, 6s, 20241015114559601405676044293.png",
    "image, 6s, 20241015114623601405772877829.jpg",
    "image, 6s, 20241015114709601405960892421.png",
    "image, 6s, 20241015114738601406082719749.png",
    "image, 6s, 20241015114759601406166650885.jpg",
    "image, 6s, 20241015114920601406498406405.png",
    "image, 6s, 20241015115114601406965624837.jpg",
    "image, 6s, 20241015115136601407057616901.png",
    "image, 6s, 20241015115154601407130267653.jpg",
    "image, 6s, 20241015115231601407281479685.png",
    "image, 6s, 20241015115250601407358185477.jpg",
    "image, 6s, 20241015115300601407398187013.png",
    "image, 6s, 20241015115321601407484030981.jpg",
    "image, 6s, 20241015115331601407528079365.png",
    "image, 6s, 20241015115358601407637635077.jpg",
    "image, 6s, 20241015124349601419890495493.png"
]