[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "qyem-demo"
dynamic = ["version"]
description = "A Python project using uv and hatch for building"
readme = "README.md"
requires-python = ">=3.10,<3.11"
keywords = ["eye-tracking", "tobii", "private"]
authors = [
  { name = "someone", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: Implementation :: CPython",
  "Programming Language :: Python :: Implementation :: PyPy",
  "Topic :: Scientific/Engineering :: Human Machine Interfaces",
  "Topic :: Software Development :: Libraries :: Python Modules",
  "Private :: Do Not Upload",
]
dependencies = [
    "pillow>=11.3.0",
    "toml>=0.10.0",
    "PySide6>=6.6.0",
]

[project.optional-dependencies]
eyetracking = [
    "tobii-research>=2.1.0",
]

[project.scripts]
qyem-demo = "qyem_demo.__main__:main"

[tool.hatch.version]
path = "src/qyem_demo/__about__.py"

[tool.hatch.envs.types]
extra-dependencies = [
  "mypy>=1.0.0",
]
[tool.hatch.envs.types.scripts]
check = "mypy --install-types --non-interactive {args:src/qyem_demo tests}"

[tool.coverage.run]
source_pkgs = ["qyem_demo", "tests"]
branch = true
parallel = true
omit = [
  "src/qyem_demo/__about__.py",
]

[tool.coverage.paths]
qyem_demo = ["src/qyem_demo", "*/qyem-demo/src/qyem_demo"]
tests = ["tests", "*/qyem-demo/tests"]

[tool.coverage.report]
exclude_lines = [
  "no cov",
  "if __name__ == .__main__.:",
  "if TYPE_CHECKING:",
]

[tool.uv]
dev-dependencies = [
  "pytest>=7.0.0",
  "pytest-cov>=4.0.0",
  "black>=23.0.0",
  "isort>=5.0.0",
  "flake8>=6.0.0",
  "mypy>=1.0.0",
  "types-toml>=0.10.8.20240310",
  "psutil>=7.0.0",
]
link-mode = "copy"

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
mypy_path = "src/qyem_demo/stubs"

# Per-module configuration
[[tool.mypy.overrides]]
module = "tobii_research.*"
ignore_missing_imports = false
