#!/usr/bin/env python3
"""
配置测试脚本
"""

import sys
import pathlib

# 添加src目录到Python路径
sys.path.insert(0, str(pathlib.Path(__file__).parent / "src"))

from qyem_demo.config import Config

def test_config():
    """测试配置加载和验证"""
    print("开始测试配置...")
    
    # 创建配置管理器
    config_manager = Config()
    
    # 加载配置
    print(f"尝试加载配置文件: {config_manager.config_path}")
    config_data = config_manager.load_config()
    
    if config_data is None:
        print("❌ 配置加载失败")
        return False
    
    print("✅ 配置加载成功")
    
    # 验证配置
    print("开始验证配置...")
    errors = config_manager.validate_config(config_data)
    
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("✅ 配置验证通过")
    
    # 显示一些配置信息
    print("\n配置信息:")
    print(f"  设备名称: {config_manager.get_config_value(config_data, 'device.name')}")
    print(f"  刺激组: {config_manager.get_config_value(config_data, 'collection-paradigm.stimulis_group')}")
    print(f"  输出频率: {config_manager.get_config_value(config_data, 'tracker_config.gaze_output_frequency')}")
    
    return True

if __name__ == "__main__":
    success = test_config()
    sys.exit(0 if success else 1) 