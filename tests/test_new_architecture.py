"""
新架构测试 - 验证简化、解耦和优化后的结构
"""

import pytest
import time
from unittest.mock import Mock, MagicMock, patch
from qyem_demo.ui.presenters.experiment_presenter import ExperimentPresenter
from qyem_demo.collector import DataCollector
from qyem_demo.paradigm import ParadigmService


class TestExperimentPresenter:
    """测试实验Presenter"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_collector = Mock(spec=DataCollector)
        self.mock_paradigm_service = Mock(spec=ParadigmService)

        # 创建实验Presenter
        self.presenter = ExperimentPresenter(
            self.mock_collector,
            self.mock_paradigm_service
        )

    def test_start_experiment_success(self):
        """测试成功开始实验"""
        # 模拟成功的会话创建
        mock_session = Mock()
        mock_session.stimuli = [<PERSON><PERSON>(), <PERSON><PERSON>(), <PERSON><PERSON>()]
        self.mock_paradigm_service.create_session.return_value = mock_session
        self.mock_collector.start_collection.return_value = True
        
        # 开始实验
        result = self.presenter.start_experiment("test_session", {"name": "test_user"})

        # 验证
        assert result is True
        self.mock_paradigm_service.create_session.assert_called_once()
        self.mock_collector.start_collection.assert_called_once()
        assert self.presenter.is_running() is True
        
        print("✅ 实验控制器开始实验测试通过")

    def test_start_experiment_already_running(self):
        """测试实验已在运行时的处理"""
        # 先开始一个实验
        mock_session = Mock()
        mock_session.stimuli = [Mock()]
        self.mock_paradigm_service.create_session.return_value = mock_session
        self.mock_collector.start_collection.return_value = True
        
        self.presenter.start_experiment("test1", {})

        # 尝试开始另一个实验
        result = self.presenter.start_experiment("test2", {})
        
        # 验证失败
        assert result is False
        
        print("✅ 实验控制器重复开始测试通过")

    def test_stop_experiment_with_data(self):
        """测试停止实验并保存数据"""
        # 先开始实验
        mock_session = Mock()
        mock_session.stimuli = [Mock()]
        self.mock_paradigm_service.create_session.return_value = mock_session
        self.mock_collector.start_collection.return_value = True
        self.presenter.start_experiment("test", {})

        # 模拟有数据
        mock_data = [{"test": "data1"}, {"test": "data2"}]
        self.mock_collector.is_collecting.return_value = True
        self.mock_collector.get_collected_data.return_value = mock_data
        self.mock_collector.stop_collection.return_value = True

        # 模拟保存成功
        save_result = {"success": True, "data_points": 2, "output_dir": "/test/path"}
        self.mock_paradigm_service.save_results.return_value = save_result

        # 停止实验
        result = self.presenter.stop_experiment(save_data=True)

        # 验证
        assert result["success"] is True
        assert result["data_points"] == 2
        self.mock_collector.stop_collection.assert_called_once()
        self.mock_paradigm_service.save_results.assert_called_once()
        assert self.presenter.is_running() is False
        
        print("✅ 实验控制器停止实验测试通过")

    def test_set_current_stimulus(self):
        """测试设置当前刺激"""
        # 先开始实验
        mock_stimulus = Mock()
        mock_stimulus.path.name = "test.jpg"
        mock_stimulus.duration = 2.0
        mock_stimulus.collect = True
        
        mock_session = Mock()
        mock_session.stimuli = [mock_stimulus]
        self.mock_paradigm_service.create_session.return_value = mock_session
        self.mock_collector.start_collection.return_value = True
        self.presenter.start_experiment("test", {})

        # 设置刺激
        result = self.presenter.set_current_stimulus(0)
        
        # 验证
        assert result is True
        self.mock_collector.set_current_stimulus.assert_called_once()
        
        print("✅ 实验控制器设置刺激测试通过")

    def test_get_experiment_status(self):
        """测试获取实验状态"""
        # 未运行状态
        status = self.presenter.get_experiment_status()
        assert status["is_running"] is False

        # 运行状态
        mock_session = Mock()
        mock_session.stimuli = [Mock(), Mock()]
        self.mock_paradigm_service.create_session.return_value = mock_session
        self.mock_collector.start_collection.return_value = True
        self.mock_collector.get_collected_data.return_value = [{"test": "data"}]

        self.presenter.start_experiment("test", {})

        status = self.presenter.get_experiment_status()
        assert status["is_running"] is True
        assert status["data_count"] == 1
        assert status["total_stimuli"] == 2
        
        print("✅ 实验控制器状态获取测试通过")


class TestSimplifiedDataCollector:
    """测试简化的数据采集器"""

    def setup_method(self):
        """设置测试环境"""
        self.collector = DataCollector(eyetracker=None, is_mock=True)

    def test_simplified_stimulus_context(self):
        """测试简化的刺激上下文"""
        # 设置刺激上下文
        self.collector.set_current_stimulus(
            index=1,
            name="test.jpg",
            path="/path/to/test.jpg",
            collect=True
        )
        
        # 验证上下文被正确设置
        with self.collector._lock:
            ctx = self.collector._stimulus_context
            assert ctx["index"] == 1
            assert ctx["name"] == "test.jpg"
            assert ctx["path"] == "/path/to/test.jpg"
            assert ctx["collect"] is True
        
        print("✅ 简化刺激上下文测试通过")

    def test_simplified_collection_flow(self):
        """测试简化的采集流程"""
        # 开始采集
        result = self.collector.start_collection()
        assert result is True
        assert self.collector.is_collecting() is True
        
        # 停止采集
        result = self.collector.stop_collection()
        assert result is True
        assert self.collector.is_collecting() is False
        
        print("✅ 简化采集流程测试通过")

    def test_callback_with_simplified_context(self):
        """测试使用简化上下文的回调"""
        # 开始采集
        self.collector.start_collection()
        
        # 设置刺激上下文
        self.collector.set_current_stimulus(
            index=0,
            name="test.jpg",
            path="/test.jpg",
            collect=True
        )
        
        # 模拟回调
        gaze_data = {
            "left_gaze_point_on_display_area": [0.5, 0.5],
            "system_time_stamp": 123456789
        }
        
        self.collector._gaze_data_callback(gaze_data)
        
        # 验证数据被保存
        data = self.collector.get_collected_data()
        assert len(data) == 1
        assert data[0]["stimulus_index"] == 0
        assert data[0]["stimulus_name"] == "test.jpg"
        assert data[0]["stimulus_collect_flag"] is True
        
        print("✅ 简化上下文回调测试通过")


class TestSimplifiedParadigmService:
    """测试简化的范式服务"""

    def setup_method(self):
        """设置测试环境"""
        self.mock_collector = Mock()
        self.mock_config = Mock()
        self.paradigm_service = ParadigmService(self.mock_config, self.mock_collector)

    def test_save_results_with_data_parameter(self):
        """测试使用数据参数的保存方法"""
        # 创建模拟会话
        mock_session = Mock()
        mock_session.name = "test_session"
        mock_session.output_dir = "/tmp/test"
        mock_session.user_info = {"name": "test_user"}
        self.paradigm_service.current_session = mock_session
        
        # 准备测试数据
        test_data = [
            {"timestamp": 1, "x": 0.5, "y": 0.5},
            {"timestamp": 2, "x": 0.6, "y": 0.6}
        ]
        
        # 模拟文件操作
        with patch('builtins.open', create=True), \
             patch('pathlib.Path.mkdir'), \
             patch('csv.DictWriter'):
            
            result = self.paradigm_service.save_results(test_data)
            
            # 验证结果
            assert result is not None
            assert result["success"] is True
            assert result["data_points"] == 2
        
        print("✅ 简化范式服务保存测试通过")

    def test_get_stimuli(self):
        """测试获取刺激列表"""
        # 无会话时
        stimuli = self.paradigm_service.get_stimuli()
        assert stimuli == []
        
        # 有会话时
        mock_session = Mock()
        mock_session.stimuli = [Mock(), Mock()]
        self.paradigm_service.current_session = mock_session
        
        stimuli = self.paradigm_service.get_stimuli()
        assert len(stimuli) == 2
        
        print("✅ 获取刺激列表测试通过")


class TestArchitectureIntegration:
    """测试新架构的集成"""

    def test_component_decoupling(self):
        """测试组件解耦"""
        # 创建独立的组件
        collector = DataCollector(eyetracker=None, is_mock=True)
        mock_config = Mock()
        paradigm_service = ParadigmService(mock_config, collector)
        presenter = ExperimentPresenter(collector, paradigm_service)

        # 验证组件可以独立工作
        assert collector.is_collecting() is False
        assert paradigm_service.get_stimuli() == []
        assert presenter.is_running() is False
        
        print("✅ 组件解耦测试通过")

    def test_signal_based_communication(self):
        """测试基于信号的通信"""
        collector = DataCollector(eyetracker=None, is_mock=True)
        mock_config = Mock()
        paradigm_service = ParadigmService(mock_config, collector)
        presenter = ExperimentPresenter(collector, paradigm_service)

        # 连接信号
        signals_received = []

        def on_experiment_started(info):
            signals_received.append(("started", info))

        def on_stimulus_changed(info):
            signals_received.append(("stimulus", info))

        presenter.set_callbacks(
            experiment_started=on_experiment_started,
            stimulus_changed=on_stimulus_changed
        )
        
        # 模拟实验流程
        mock_session = Mock()
        mock_stimulus = Mock()
        mock_stimulus.path.name = "test.jpg"
        mock_stimulus.duration = 1.0
        mock_stimulus.collect = True
        mock_session.stimuli = [mock_stimulus]
        
        paradigm_service.create_session = Mock(return_value=mock_session)
        
        # 开始实验
        presenter.start_experiment("test", {})
        presenter.set_current_stimulus(0)
        
        # 验证信号被发射
        assert len(signals_received) >= 1
        assert signals_received[0][0] == "started"
        
        print("✅ 信号通信测试通过")
