"""
测试表单完整性验证
"""

import pytest
from unittest.mock import Mock, patch
from PySide6.QtWidgets import QApplication
import sys

# 确保QApplication存在
if not QApplication.instance():
    app = QApplication(sys.argv)

from qyem_demo.ui.main_window import MainWindow
from qyem_demo.ui.presenters import HomePresenter, AssessmentPresenter


class TestFormValidation:
    """测试表单完整性验证"""

    def setup_method(self):
        """设置测试环境"""
        # 创建模拟的服务
        self.mock_paradigm_service = Mock()
        self.mock_et_session = Mock()

        # 创建MainWindow
        self.main_window = MainWindow(
            paradigm_service=self.mock_paradigm_service,
            et_session=self.mock_et_session,
            dry_run=True
        )

    def test_validate_complete_data_missing_user_info(self):
        """测试缺少用户信息时的验证"""
        # 清空用户信息
        self.main_window.user_info = None
        self.main_window.assessment_data = {"mmse": {}, "moca": {}}
        
        # 验证应该失败
        result = self.main_window._validate_complete_data()
        assert result is False

    def test_validate_complete_data_invalid_user_info(self):
        """测试用户信息不完整时的验证"""
        # 设置不完整的用户信息
        self.main_window.user_info = {"name": ""}  # 姓名为空
        self.main_window.assessment_data = {"mmse": {}, "moca": {}}
        
        # 验证应该失败
        result = self.main_window._validate_complete_data()
        assert result is False

    def test_validate_complete_data_missing_assessment_data(self):
        """测试缺少评估数据时的验证"""
        # 设置完整的用户信息
        self.main_window.user_info = {
            "name": "测试用户",
            "birth_date": "1990-01-01",
            "education": "本科",
            "gender": 1
        }
        # 清空评估数据
        self.main_window.assessment_data = None
        
        # 验证应该失败
        result = self.main_window._validate_complete_data()
        assert result is False

    def test_validate_complete_data_invalid_assessment_data(self):
        """测试评估数据不完整时的验证"""
        # 设置完整的用户信息
        self.main_window.user_info = {
            "name": "测试用户",
            "birth_date": "1990-01-01",
            "education": "本科",
            "gender": 1
        }
        # 设置不完整的评估数据
        self.main_window.assessment_data = {
            "mmse": {},  # 空的MMSE数据
            "moca": {}   # 空的MoCA数据
        }
        
        # 验证应该失败
        result = self.main_window._validate_complete_data()
        assert result is False

    def test_validate_complete_data_success(self):
        """测试完整数据的验证成功"""
        # 设置完整的用户信息
        self.main_window.user_info = {
            "name": "测试用户",
            "birth_date": "1990-01-01",
            "education": "本科",
            "gender": 1
        }
        
        # 设置完整的评估数据
        mmse_data = {
            "orientation": 10,
            "instant_memory": 3,
            "calculation": 5,
            "short_term_memory": 3,
            "naming": 2,
            "repetition": 1,
            "reading": 1,
            "execution": 3,
            "writing": 1,
            "structure_imitation": 1
        }
        
        moca_data = {
            "visuospatial_execution": 2,
            "clock_drawing": 3,
            "naming": 3,
            "attention": 3,
            "calculation": 3,
            "language": 3,
            "abstraction": 2,
            "delayed_recall": 5,
            "orientation": 6
        }
        
        self.main_window.assessment_data = {
            "mmse": mmse_data,
            "moca": moca_data
        }
        
        # 验证应该成功
        result = self.main_window._validate_complete_data()
        assert result is True

    def test_assessment_completed_with_incomplete_data(self):
        """测试评估完成但数据不完整的情况"""
        # 设置不完整的用户信息
        self.main_window.user_info = {"name": ""}  # 姓名为空
        
        # 模拟评估数据
        assessment_data = {
            "mmse": {},
            "moca": {}
        }
        
        # 模拟assessment_page的show_error方法
        self.main_window.assessment_page = Mock()
        
        # 调用_on_assessment_completed
        self.main_window._on_assessment_completed(assessment_data)
        
        # 验证显示了错误信息
        self.main_window.assessment_page.show_error.assert_called()
        
        # 验证没有开始实验流程（通过检查是否调用了校准相关方法）
        # 这里我们通过mock来验证没有调用_skip_calibration或_run_calibration
        with patch.object(self.main_window, '_skip_calibration') as mock_skip, \
             patch.object(self.main_window, '_run_calibration') as mock_run:
            
            self.main_window._on_assessment_completed(assessment_data)
            
            # 验证没有调用校准方法
            mock_skip.assert_not_called()
            mock_run.assert_not_called()


if __name__ == "__main__":
    pytest.main([__file__])
