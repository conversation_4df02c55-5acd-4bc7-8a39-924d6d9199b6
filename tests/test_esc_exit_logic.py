"""
ESC退出逻辑测试 - 验证优化后的退出和数据保存逻辑
"""

import pytest
from unittest.mock import Mock, MagicMock, patch


class MockPlaybackWindow:
    """模拟播放窗口，避免Qt依赖"""

    def __init__(self):
        self.paradigm_service = None
        self.current_session = None
        self.is_playing = True

    def _handle_user_exit(self) -> None:
        """处理用户主动退出"""
        if not self.is_playing:
            return

        # 检查是否有已采集的数据
        data_count = 0
        if self.paradigm_service and self.paradigm_service.collector:
            data = self.paradigm_service.collector.get_collected_data()
            data_count = len(data)

        if data_count > 0:
            print(f"📊 检测到已采集 {data_count} 个数据点")
            # 有数据时，默认保存数据并标记为用户中断
            self.stop_playback(cancelled_by_user=True, save_data=True)
        else:
            print("📊 未检测到采集数据")
            # 无数据时，直接退出不保存
            self.stop_playback(cancelled_by_user=True, save_data=False)

    def stop_playback(self, cancelled_by_user: bool = False, save_data: bool = None) -> None:
        """停止播放"""
        if not self.is_playing:
            return

        print("⏹️ 停止播放")
        self.is_playing = False

        # 完成播放流程
        self._finish_playback(cancelled=cancelled_by_user, save_data=save_data)

    def _finish_playback(self, cancelled: bool = False, save_data: bool = None) -> None:
        """完成播放流程"""
        # 确定是否保存数据
        should_save = save_data if save_data is not None else (not cancelled)

        print(f"🏁 完成播放流程 (取消: {'是' if cancelled else '否'}, 保存数据: {'是' if should_save else '否'})")

        if not self.paradigm_service:
            print("❌ 范式服务未初始化")
            return

        # 1. 线程安全地停止数据采集
        data_count = 0
        if self.paradigm_service.collector and self.paradigm_service.collector.is_collecting():
            print("⏹️ 结束整个范式的数据采集")
            # 先获取数据计数，再停止采集
            data_count = len(self.paradigm_service.collector.get_collected_data())
            self.paradigm_service.stop_collection(cancelled=cancelled)

        # 2. 清除刺激信息
        if self.paradigm_service.collector:
            self.paradigm_service.collector.clear_current_stimulus()

        # 3. 更新会话状态
        if self.current_session:
            if cancelled and should_save:
                self.current_session.status = "interrupted"  # 用户中断但保存数据
            elif cancelled:
                self.current_session.status = "cancelled"    # 用户取消且不保存
            else:
                self.current_session.status = "finished"     # 正常完成

        # 4. 根据决定保存或丢弃数据
        if should_save and data_count > 0:
            print(f"💾 保存实验结果... (共 {data_count} 个数据点)")
            save_result = self.paradigm_service.save_results()
            if save_result and save_result.get("success"):
                print(f"✅ 实验数据已保存，共 {save_result.get('data_points', 0)} 个数据点")
                if cancelled:
                    print("📝 注意：实验被用户中断，但数据已保存")
            else:
                print("❌ 数据保存失败")
        elif should_save and data_count == 0:
            print("⚠️ 没有数据需要保存")
        else:
            print("🗑️ 根据用户选择，不保存实验数据")

    def _emit_completion_signal(self, cancelled: bool, save_result: dict) -> None:
        """发射播放完成信号"""
        if cancelled:
            # 用户中断播放
            print("📡 发射播放停止信号")
            if hasattr(self, 'playback_stopped') and hasattr(self.playback_stopped, 'emit'):
                self.playback_stopped.emit(save_result or {"success": False, "error": "未知错误"})
        else:
            # 正常完成播放
            print("📡 发射播放完成信号")
            if hasattr(self, 'playback_finished') and hasattr(self.playback_finished, 'emit'):
                self.playback_finished.emit(save_result or {"success": False, "error": "未知错误"})


class TestEscExitLogic:
    """ESC退出逻辑测试"""

    def setup_method(self):
        """设置测试环境"""
        # 创建模拟的范式服务和采集器
        self.mock_collector = Mock()
        self.mock_paradigm_service = Mock()
        self.mock_paradigm_service.collector = self.mock_collector

        # 创建模拟播放窗口
        self.playback_window = MockPlaybackWindow()
        self.playback_window.paradigm_service = self.mock_paradigm_service
        self.playback_window.is_playing = True

    def test_esc_exit_with_data_saves_automatically(self):
        """测试有数据时ESC退出自动保存"""
        # 模拟有采集数据
        mock_data = [{"test": "data1"}, {"test": "data2"}, {"test": "data3"}]
        self.mock_collector.get_collected_data.return_value = mock_data
        self.mock_collector.is_collecting.return_value = True
        
        # 模拟保存成功
        self.mock_paradigm_service.save_results.return_value = {
            "success": True,
            "data_points": len(mock_data)
        }
        
        # 调用用户退出处理
        self.playback_window._handle_user_exit()
        
        # 验证数据被保存
        self.mock_paradigm_service.save_results.assert_called_once()
        
        # 验证会话状态被设置为中断
        if self.playback_window.current_session:
            assert self.playback_window.current_session.status == "interrupted"
        
        print("✅ 有数据时ESC退出自动保存测试通过")

    def test_esc_exit_without_data_no_save(self):
        """测试无数据时ESC退出不保存"""
        # 模拟无采集数据
        self.mock_collector.get_collected_data.return_value = []
        self.mock_collector.is_collecting.return_value = False
        
        # 调用用户退出处理
        self.playback_window._handle_user_exit()
        
        # 验证数据没有被保存
        self.mock_paradigm_service.save_results.assert_not_called()
        
        print("✅ 无数据时ESC退出不保存测试通过")

    def test_finish_playback_with_save_data_true(self):
        """测试明确指定保存数据"""
        # 模拟有数据
        mock_data = [{"test": "data1"}]
        self.mock_collector.get_collected_data.return_value = mock_data
        self.mock_collector.is_collecting.return_value = True
        
        # 模拟保存成功
        self.mock_paradigm_service.save_results.return_value = {
            "success": True,
            "data_points": len(mock_data)
        }
        
        # 创建模拟会话
        mock_session = Mock()
        self.playback_window.current_session = mock_session
        
        # 调用完成播放，明确指定保存数据
        self.playback_window._finish_playback(cancelled=True, save_data=True)
        
        # 验证数据被保存
        self.mock_paradigm_service.save_results.assert_called_once()
        
        # 验证会话状态为中断
        assert mock_session.status == "interrupted"
        
        print("✅ 明确指定保存数据测试通过")

    def test_finish_playback_with_save_data_false(self):
        """测试明确指定不保存数据"""
        # 模拟有数据
        mock_data = [{"test": "data1"}]
        self.mock_collector.get_collected_data.return_value = mock_data
        self.mock_collector.is_collecting.return_value = True
        
        # 创建模拟会话
        mock_session = Mock()
        self.playback_window.current_session = mock_session
        
        # 调用完成播放，明确指定不保存数据
        self.playback_window._finish_playback(cancelled=True, save_data=False)
        
        # 验证数据没有被保存
        self.mock_paradigm_service.save_results.assert_not_called()
        
        # 验证会话状态为取消
        assert mock_session.status == "cancelled"
        
        print("✅ 明确指定不保存数据测试通过")

    def test_normal_completion_saves_data(self):
        """测试正常完成保存数据"""
        # 模拟有数据
        mock_data = [{"test": "data1"}, {"test": "data2"}]
        self.mock_collector.get_collected_data.return_value = mock_data
        self.mock_collector.is_collecting.return_value = True
        
        # 模拟保存成功
        self.mock_paradigm_service.save_results.return_value = {
            "success": True,
            "data_points": len(mock_data)
        }
        
        # 创建模拟会话
        mock_session = Mock()
        self.playback_window.current_session = mock_session
        
        # 调用完成播放，正常完成
        self.playback_window._finish_playback(cancelled=False)
        
        # 验证数据被保存
        self.mock_paradigm_service.save_results.assert_called_once()
        
        # 验证会话状态为完成
        assert mock_session.status == "finished"
        
        print("✅ 正常完成保存数据测试通过")

    def test_thread_safety_during_exit(self):
        """测试退出过程中的线程安全"""
        # 模拟采集器正在采集
        self.mock_collector.is_collecting.return_value = True
        
        # 模拟数据获取和停止采集的调用顺序
        call_order = []
        
        def mock_get_data():
            call_order.append("get_data")
            return [{"test": "data"}]
        
        def mock_stop_collection(cancelled=False):
            call_order.append("stop_collection")
            return True
        
        self.mock_collector.get_collected_data.side_effect = mock_get_data
        self.mock_paradigm_service.stop_collection.side_effect = mock_stop_collection
        
        # 模拟保存成功
        self.mock_paradigm_service.save_results.return_value = {
            "success": True,
            "data_points": 1
        }
        
        # 调用完成播放
        self.playback_window._finish_playback(cancelled=True, save_data=True)
        
        # 验证调用顺序：先获取数据计数，再停止采集
        assert call_order == ["get_data", "stop_collection"]
        
        print("✅ 退出过程线程安全测试通过")

    def test_no_data_no_save_message(self):
        """测试无数据时的提示信息"""
        # 模拟无数据
        self.mock_collector.get_collected_data.return_value = []
        self.mock_collector.is_collecting.return_value = False
        
        # 创建模拟会话
        mock_session = Mock()
        self.playback_window.current_session = mock_session
        
        # 调用完成播放，指定保存但无数据
        self.playback_window._finish_playback(cancelled=False, save_data=True)
        
        # 验证没有调用保存（因为无数据）
        self.mock_paradigm_service.save_results.assert_not_called()
        
        # 验证会话状态为完成
        assert mock_session.status == "finished"
        
        print("✅ 无数据时的提示信息测试通过")


class TestPlaybackWindowIntegration:
    """播放窗口集成测试"""

    def test_stop_playback_parameter_passing(self):
        """测试stop_playback参数传递"""
        # 测试第一组参数
        window1 = MockPlaybackWindow()
        window1.is_playing = True
        window1._finish_playback = Mock()

        window1.stop_playback(cancelled_by_user=True, save_data=True)
        window1._finish_playback.assert_called_with(cancelled=True, save_data=True)

        # 测试第二组参数
        window2 = MockPlaybackWindow()
        window2.is_playing = True
        window2._finish_playback = Mock()

        window2.stop_playback(cancelled_by_user=False, save_data=None)
        window2._finish_playback.assert_called_with(cancelled=False, save_data=None)

        print("✅ stop_playback参数传递测试通过")

    def test_signal_emission_on_completion(self):
        """测试播放完成时信号发射"""
        window = MockPlaybackWindow()

        # 模拟信号发射方法
        window.playback_finished = Mock()
        window.playback_stopped = Mock()

        # 测试正常完成
        save_result = {"success": True, "data_points": 5, "output_dir": "/test/path"}
        window._emit_completion_signal(cancelled=False, save_result=save_result)
        window.playback_finished.emit.assert_called_once_with(save_result)

        # 测试用户中断
        window.playback_finished.reset_mock()
        window._emit_completion_signal(cancelled=True, save_result=save_result)
        window.playback_stopped.emit.assert_called_once_with(save_result)

        print("✅ 信号发射测试通过")
