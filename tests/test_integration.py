"""
集成测试 - 验证重构后的采集系统整体工作流程
"""

import pytest
import time
from unittest.mock import Mock, patch
from qyem_demo.collector import DataCollector


class TestIntegration:
    """集成测试"""

    def test_complete_collection_workflow(self):
        """测试完整的采集工作流程"""
        # 创建模拟采集器
        collector = DataCollector(eyetracker=None, is_mock=True)
        
        # 1. 开始采集
        assert collector.start_collection() is True
        assert collector.is_collecting() is True
        
        # 2. 模拟多个刺激的数据采集
        stimuli_data = [
            {"index": 0, "name": "rest.jpg", "collect": False},
            {"index": 1, "name": "target1.jpg", "collect": True},
            {"index": 2, "name": "target2.jpg", "collect": True},
            {"index": 3, "name": "break.jpg", "collect": False},
            {"index": 4, "name": "target3.jpg", "collect": True},
        ]
        
        collected_count = 0
        for stimulus in stimuli_data:
            # 设置当前刺激
            collector.set_current_stimulus(
                index=stimulus["index"],
                name=stimulus["name"],
                path=f"/path/to/{stimulus['name']}",
                collect=stimulus["collect"]
            )
            
            # 模拟眼动数据
            gaze_data = {
                "left_gaze_point_on_display_area": [0.5, 0.5],
                "right_gaze_point_on_display_area": [0.5, 0.5],
                "system_time_stamp": 123456789 + stimulus["index"]
            }
            
            # 调用回调（模拟眼动仪数据）
            collector._gaze_data_callback(gaze_data)
            
            if stimulus["collect"]:
                collected_count += 1
        
        # 3. 验证只有标记为采集的刺激数据被保存
        collected_data = collector.get_collected_data()
        assert len(collected_data) == collected_count  # 应该是3个（target1, target2, target3）
        
        # 4. 验证数据包含正确的刺激上下文
        target_indices = [1, 2, 4]  # 只有这些刺激被采集
        for i, data_point in enumerate(collected_data):
            expected_index = target_indices[i]
            assert data_point["stimulus_index"] == expected_index
            assert data_point["stimulus_name"] == f"target{expected_index if expected_index <= 2 else 3}.jpg"
            assert data_point["stimulus_collect_flag"] is True
        
        # 5. 停止采集
        assert collector.stop_collection() is True
        assert collector.is_collecting() is False
        
        # 6. 验证数据仍然存在（不会因为停止采集而丢失）
        final_data = collector.get_collected_data()
        assert len(final_data) == collected_count
        
        print(f"✅ 集成测试通过：采集了 {len(final_data)} 个数据点")

    def test_thread_safety_simulation(self):
        """测试线程安全性（模拟并发访问）"""
        collector = DataCollector(eyetracker=None, is_mock=True)
        
        # 开始采集
        collector.start_collection()
        
        # 模拟并发设置刺激和获取数据
        import threading
        
        def set_stimulus_worker(index):
            for i in range(10):
                collector.set_current_stimulus(
                    index=index * 10 + i,
                    name=f"stimulus_{index}_{i}.jpg",
                    collect=True
                )
                time.sleep(0.001)  # 短暂延迟
        
        def get_data_worker():
            for i in range(10):
                data = collector.get_collected_data()
                status = collector.get_collection_status()
                time.sleep(0.001)  # 短暂延迟
        
        # 创建多个线程
        threads = []
        for i in range(3):
            t1 = threading.Thread(target=set_stimulus_worker, args=(i,))
            t2 = threading.Thread(target=get_data_worker)
            threads.extend([t1, t2])
        
        # 启动所有线程
        for thread in threads:
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 停止采集
        collector.stop_collection()
        
        print("✅ 线程安全测试通过：没有发生死锁或数据竞争")

    def test_data_preservation_across_sessions(self):
        """测试跨会话的数据保留"""
        collector = DataCollector(eyetracker=None, is_mock=True)
        
        # 第一次采集
        collector.start_collection()
        collector.set_current_stimulus(0, "session1_stimulus.jpg", collect=True)
        
        gaze_data1 = {
            "left_gaze_point_on_display_area": [0.3, 0.3],
            "system_time_stamp": 123456789
        }
        collector._gaze_data_callback(gaze_data1)
        collector.stop_collection()
        
        # 验证第一次采集的数据
        data_after_first = collector.get_collected_data()
        assert len(data_after_first) == 1
        
        # 第二次采集（不清空数据）
        collector.start_collection()
        collector.set_current_stimulus(1, "session2_stimulus.jpg", collect=True)
        
        gaze_data2 = {
            "left_gaze_point_on_display_area": [0.7, 0.7],
            "system_time_stamp": 123456790
        }
        collector._gaze_data_callback(gaze_data2)
        collector.stop_collection()
        
        # 验证两次采集的数据都被保留
        data_after_second = collector.get_collected_data()
        assert len(data_after_second) == 2
        
        # 验证数据来自不同的会话
        assert data_after_second[0]["stimulus_name"] == "session1_stimulus.jpg"
        assert data_after_second[1]["stimulus_name"] == "session2_stimulus.jpg"
        
        print("✅ 数据保留测试通过：多次采集的数据都被正确保留")
