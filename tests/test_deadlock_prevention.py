"""
死锁预防测试 - 验证 _gaze_data_callback 和 stop_collection 不会死锁
"""

import pytest
import time
import threading
from unittest.mock import Mock, patch
from qyem_demo.collector import DataCollector


class TestDeadlockPrevention:
    """死锁预防测试"""

    def test_callback_stop_collection_no_deadlock(self):
        """测试回调和停止采集不会死锁"""
        collector = DataCollector(eyetracker=None, is_mock=True)
        
        # 开始采集
        collector.start_collection()
        collector.set_current_stimulus(0, "test.jpg", collect=True)
        
        # 用于同步线程的事件
        callback_started = threading.Event()
        callback_can_continue = threading.Event()
        stop_started = threading.Event()
        
        def slow_callback():
            """模拟一个慢速的回调处理"""
            callback_started.set()
            
            # 模拟回调处理
            gaze_data = {
                "left_gaze_point_on_display_area": [0.5, 0.5],
                "system_time_stamp": 123456789
            }
            
            # 等待停止采集开始
            callback_can_continue.wait(timeout=2.0)
            
            # 调用回调
            collector._gaze_data_callback(gaze_data)
        
        def stop_collection_thread():
            """停止采集的线程"""
            # 等待回调开始
            callback_started.wait(timeout=2.0)
            stop_started.set()
            
            # 短暂延迟确保回调正在执行
            time.sleep(0.1)
            
            # 停止采集
            result = collector.stop_collection()
            assert result is True
            
            # 允许回调继续
            callback_can_continue.set()
        
        # 启动两个线程
        callback_thread = threading.Thread(target=slow_callback)
        stop_thread = threading.Thread(target=stop_collection_thread)
        
        callback_thread.start()
        stop_thread.start()
        
        # 等待两个线程完成（如果死锁，这里会超时）
        callback_thread.join(timeout=5.0)
        stop_thread.join(timeout=5.0)
        
        # 验证线程都已完成
        assert not callback_thread.is_alive(), "回调线程应该已完成"
        assert not stop_thread.is_alive(), "停止采集线程应该已完成"
        
        print("✅ 死锁预防测试通过：回调和停止采集可以并发执行")

    def test_high_frequency_callback_with_stop(self):
        """测试高频回调与停止采集的并发"""
        collector = DataCollector(eyetracker=None, is_mock=True)
        
        # 开始采集
        collector.start_collection()
        collector.set_current_stimulus(0, "test.jpg", collect=True)
        
        callback_count = 0
        stop_called = threading.Event()
        
        def high_frequency_callbacks():
            """高频率调用回调"""
            nonlocal callback_count
            
            for i in range(100):
                if stop_called.is_set():
                    break
                    
                gaze_data = {
                    "left_gaze_point_on_display_area": [0.5, 0.5],
                    "system_time_stamp": 123456789 + i
                }
                
                collector._gaze_data_callback(gaze_data)
                callback_count += 1
                time.sleep(0.001)  # 1ms间隔，模拟高频数据
        
        def delayed_stop():
            """延迟停止采集"""
            time.sleep(0.05)  # 50ms后停止
            stop_called.set()
            result = collector.stop_collection()
            assert result is True
        
        # 启动线程
        callback_thread = threading.Thread(target=high_frequency_callbacks)
        stop_thread = threading.Thread(target=delayed_stop)
        
        start_time = time.time()
        callback_thread.start()
        stop_thread.start()
        
        # 等待完成
        callback_thread.join(timeout=3.0)
        stop_thread.join(timeout=3.0)
        end_time = time.time()
        
        # 验证
        assert not callback_thread.is_alive(), "回调线程应该已完成"
        assert not stop_thread.is_alive(), "停止线程应该已完成"
        assert end_time - start_time < 2.0, "不应该发生死锁导致的长时间等待"
        
        print(f"✅ 高频并发测试通过：处理了 {callback_count} 个回调，耗时 {end_time - start_time:.3f}s")

    def test_try_lock_behavior(self):
        """测试非阻塞锁的行为"""
        collector = DataCollector(eyetracker=None, is_mock=True)

        # 开始采集
        collector.start_collection()
        collector.set_current_stimulus(0, "test.jpg", collect=True)

        # 准备测试数据
        gaze_data = {
            "left_gaze_point_on_display_area": [0.5, 0.5],
            "system_time_stamp": 123456789
        }

        # 使用线程来模拟锁竞争
        lock_acquired = threading.Event()
        test_completed = threading.Event()
        callback_time = [0]  # 使用列表来存储可变值

        def lock_holder():
            """持有锁的线程"""
            with collector._lock:
                lock_acquired.set()
                # 持有锁一段时间
                test_completed.wait(timeout=1.0)

        def callback_caller():
            """调用回调的线程"""
            # 等待锁被获取
            lock_acquired.wait(timeout=1.0)

            # 调用回调（应该立即返回）
            start_time = time.time()
            collector._gaze_data_callback(gaze_data)
            end_time = time.time()

            callback_time[0] = end_time - start_time
            test_completed.set()

        # 启动线程
        lock_thread = threading.Thread(target=lock_holder)
        callback_thread = threading.Thread(target=callback_caller)

        lock_thread.start()
        callback_thread.start()

        # 等待完成
        lock_thread.join(timeout=2.0)
        callback_thread.join(timeout=2.0)

        # 验证回调立即返回（没有阻塞）
        assert callback_time[0] < 0.01, f"回调应该立即返回，实际耗时: {callback_time[0]:.6f}s"

        # 验证数据没有被保存（因为无法获取锁）
        data = collector.get_collected_data()
        initial_count = len(data)

        # 现在锁释放了，回调应该能正常工作
        collector._gaze_data_callback(gaze_data)
        data = collector.get_collected_data()
        assert len(data) == initial_count + 1, "现在数据应该被保存"

        print("✅ 非阻塞锁测试通过：回调在锁被占用时立即返回")

    def test_concurrent_operations_stress_test(self):
        """并发操作压力测试"""
        collector = DataCollector(eyetracker=None, is_mock=True)
        
        # 开始采集
        collector.start_collection()
        
        results = {"callbacks": 0, "status_checks": 0, "stimulus_sets": 0}
        stop_event = threading.Event()
        
        def callback_worker():
            """回调工作线程"""
            while not stop_event.is_set():
                gaze_data = {
                    "left_gaze_point_on_display_area": [0.5, 0.5],
                    "system_time_stamp": time.time() * 1000000
                }
                collector._gaze_data_callback(gaze_data)
                results["callbacks"] += 1
                time.sleep(0.001)
        
        def status_worker():
            """状态检查工作线程"""
            while not stop_event.is_set():
                collector.get_collection_status()
                collector.is_collecting()
                results["status_checks"] += 1
                time.sleep(0.002)
        
        def stimulus_worker():
            """刺激设置工作线程"""
            index = 0
            while not stop_event.is_set():
                collector.set_current_stimulus(index, f"stimulus_{index}.jpg", collect=True)
                results["stimulus_sets"] += 1
                index += 1
                time.sleep(0.005)
        
        # 启动多个工作线程
        threads = [
            threading.Thread(target=callback_worker),
            threading.Thread(target=status_worker),
            threading.Thread(target=stimulus_worker),
        ]
        
        for thread in threads:
            thread.start()
        
        # 运行一段时间
        time.sleep(0.2)
        
        # 停止所有线程
        stop_event.set()
        
        # 停止采集
        collector.stop_collection()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=2.0)
            assert not thread.is_alive(), "所有线程都应该完成"
        
        print(f"✅ 并发压力测试通过：")
        print(f"   - 回调: {results['callbacks']} 次")
        print(f"   - 状态检查: {results['status_checks']} 次") 
        print(f"   - 刺激设置: {results['stimulus_sets']} 次")
