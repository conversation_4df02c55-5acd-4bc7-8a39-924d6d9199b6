"""
测试采集器改进功能
"""

import pytest
import time
from unittest.mock import Mock, MagicMock, patch
from qyem_demo.collector import DataCollector


class TestCollectorImprovements:
    """测试采集器改进功能"""

    def setup_method(self):
        """设置测试环境"""
        # 创建模拟眼动仪
        self.mock_eyetracker = Mock()

        # 模拟 tobii_research 模块
        with patch('qyem_demo.collector.tr') as mock_tr:
            mock_tr.EYETRACKER_GAZE_DATA = "gaze_data"
            self.collector = DataCollector(eyetracker=self.mock_eyetracker, is_mock=False)

    @patch('qyem_demo.collector.tr')
    def test_start_collection_no_parameters(self, mock_tr):
        """测试 start_collection 不需要参数"""
        mock_tr.EYETRACKER_GAZE_DATA = "gaze_data"

        # 测试方法可以无参数调用
        result = self.collector.start_collection()
        assert result is True

        # 验证眼动仪订阅被调用
        self.mock_eyetracker.subscribe_to.assert_called_once()

    def test_data_persistence_across_stimuli(self):
        """测试多个刺激的数据持久化"""
        # 设置采集状态
        self.collector._is_collecting = True
        self.collector._start_time = time.time()

        # 模拟第一个刺激的数据
        self.collector.set_current_stimulus(0, "stimulus1.jpg", "/path/to/stimulus1.jpg", True)
        gaze_data1 = {"left_gaze_point_on_display_area": [0.3, 0.3], "system_time_stamp": 123456789}
        self.collector._gaze_data_callback(gaze_data1)

        # 模拟第二个刺激的数据
        self.collector.set_current_stimulus(1, "stimulus2.jpg", "/path/to/stimulus2.jpg", True)
        gaze_data2 = {"left_gaze_point_on_display_area": [0.7, 0.7], "system_time_stamp": 123456790}
        self.collector._gaze_data_callback(gaze_data2)

        # 验证两个刺激的数据都被保存
        collected_data = self.collector.get_collected_data()
        assert len(collected_data) == 2

        # 验证数据包含正确的刺激信息
        assert collected_data[0]["stimulus_index"] == 0
        assert collected_data[0]["stimulus_name"] == "stimulus1.jpg"
        assert collected_data[1]["stimulus_index"] == 1
        assert collected_data[1]["stimulus_name"] == "stimulus2.jpg"

    def test_clear_data_method(self):
        """测试手动清空数据方法"""
        # 添加一些测试数据
        self.collector._gaze_data_storage = [{"test": "data1"}, {"test": "data2"}]

        # 手动清空数据
        self.collector.clear_data()

        # 验证数据被清空
        assert len(self.collector._gaze_data_storage) == 0

    def test_stimulus_context_in_callback(self):
        """测试回调中的刺激上下文信息"""
        # 设置采集状态
        self.collector._is_collecting = True
        self.collector._start_time = time.time()
        
        # 设置当前刺激信息
        self.collector.set_current_stimulus(
            index=1,
            name="test_stimulus.jpg",
            path="/path/to/test_stimulus.jpg",
            collect=True
        )
        
        # 模拟眼动数据
        gaze_data = {
            "left_gaze_point_on_display_area": [0.5, 0.5],
            "right_gaze_point_on_display_area": [0.5, 0.5],
            "system_time_stamp": 123456789
        }
        
        # 调用回调
        self.collector._gaze_data_callback(gaze_data)
        
        # 验证数据被保存且包含刺激上下文
        assert len(self.collector._gaze_data_storage) == 1
        saved_data = self.collector._gaze_data_storage[0]
        
        assert saved_data["stimulus_index"] == 1
        assert saved_data["stimulus_name"] == "test_stimulus.jpg"
        assert saved_data["stimulus_path"] == "/path/to/test_stimulus.jpg"
        assert saved_data["stimulus_collect_flag"] is True

    def test_stimulus_collect_filtering(self):
        """测试基于刺激的采集过滤"""
        # 设置采集状态
        self.collector._is_collecting = True
        self.collector._start_time = time.time()
        
        # 设置当前刺激信息，不采集
        self.collector.set_current_stimulus(
            index=1,
            name="no_collect_stimulus.jpg",
            path="/path/to/no_collect_stimulus.jpg",
            collect=False
        )
        
        # 模拟眼动数据
        gaze_data = {
            "left_gaze_point_on_display_area": [0.5, 0.5],
            "right_gaze_point_on_display_area": [0.5, 0.5],
            "system_time_stamp": 123456789
        }
        
        # 调用回调
        self.collector._gaze_data_callback(gaze_data)
        
        # 验证数据没有被保存（因为 collect=False）
        assert len(self.collector._gaze_data_storage) == 0

    @patch('qyem_demo.collector.tr')
    def test_collect_for_duration_no_parameters(self, mock_tr):
        """测试 collect_for_duration 不需要 session_name 参数"""
        mock_tr.EYETRACKER_GAZE_DATA = "gaze_data"

        # 测试方法可以只用必需参数调用
        result = self.collector.collect_for_duration(duration_s=0.1)

        # 验证返回了数据列表
        assert isinstance(result, list)

        # 验证眼动仪订阅和取消订阅被调用
        self.mock_eyetracker.subscribe_to.assert_called()
        self.mock_eyetracker.unsubscribe_from.assert_called()


class TestMockCollector:
    """测试模拟模式下的采集器"""

    def test_mock_collector_start_collection(self):
        """测试模拟模式下的 start_collection"""
        collector = DataCollector(eyetracker=None, is_mock=True)
        
        # 模拟模式下应该直接返回 True
        result = collector.start_collection()
        assert result is True

    def test_mock_collector_stop_collection(self):
        """测试模拟模式下的 stop_collection"""
        collector = DataCollector(eyetracker=None, is_mock=True)

        # 先开始采集
        collector.start_collection()

        # 然后停止采集应该返回 True
        result = collector.stop_collection()
        assert result is True
