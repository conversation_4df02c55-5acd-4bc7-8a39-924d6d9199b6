"""
实验控制器 - 协调所有组件，管理实验生命周期
"""

from typing import TYPE_CHECKING, Optional, Any, Callable
import threading
import time

if TYPE_CHECKING:
    from qyem_demo.collector import DataCollector
    from qyem_demo.paradigm import ParadigmService


class ExperimentController:
    """
    实验控制器 - 负责协调所有组件，管理实验生命周期
    
    职责：
    1. 管理实验状态
    2. 协调数据采集器和范式服务
    3. 处理业务逻辑
    4. 统一错误处理
    5. 线程安全的状态管理
    """
    
    # 回调函数定义
    experiment_started_callback: Optional[Callable[[dict], None]] = None
    experiment_finished_callback: Optional[Callable[[dict], None]] = None
    experiment_stopped_callback: Optional[Callable[[dict], None]] = None
    stimulus_changed_callback: Optional[Callable[[dict], None]] = None
    error_occurred_callback: Optional[Callable[[str], None]] = None
    status_updated_callback: Optional[Callable[[dict], None]] = None

    def __init__(self, collector: "DataCollector", paradigm_service: "ParadigmService"):
        """
        初始化实验控制器
        
        Args:
            collector: 数据采集器
            paradigm_service: 范式服务
        """
        
        self.collector = collector
        self.paradigm_service = paradigm_service
        
        # 实验状态
        self._lock = threading.Lock()
        self._is_running = False
        self._current_session = None
        self._start_time = None
        self._stimuli = []
        self._current_stimulus_index = 0
        


    def start_experiment(self, session_name: str, user_info: dict) -> bool:
        """
        开始实验
        
        Args:
            session_name: 会话名称
            user_info: 用户信息
            
        Returns:
            是否成功开始
        """
        with self._lock:
            if self._is_running:
                if self.error_occurred_callback:
                    self.error_occurred_callback("实验已在运行中")
                return False
        
        try:
            # 1. 创建会话
            self._current_session = self.paradigm_service.create_session(session_name, user_info)
            if not self._current_session:
                if self.error_occurred_callback:
                    self.error_occurred_callback("创建实验会话失败")
                return False
            
            # 2. 获取刺激列表
            self._stimuli = self._current_session.stimuli
            if not self._stimuli:
                if self.error_occurred_callback:
                    self.error_occurred_callback("没有可用的刺激")
                return False
            
            # 3. 开始数据采集
            if not self.collector.start_collection():
                if self.error_occurred_callback:
                    self.error_occurred_callback("启动数据采集失败")
                return False
            
            # 4. 更新状态
            with self._lock:
                self._is_running = True
                self._start_time = time.time()
                self._current_stimulus_index = 0
            

            
            # 6. 调用开始回调
            session_info = {
                "session_name": session_name,
                "user_info": user_info,
                "stimulus_count": len(self._stimuli)
            }
            if self.experiment_started_callback:
                self.experiment_started_callback(session_info)
            
            print(f"✅ 实验开始: {session_name}, 共 {len(self._stimuli)} 个刺激")
            return True
            
        except Exception as e:
            if self.error_occurred_callback:
                self.error_occurred_callback(f"启动实验失败: {e}")
            return False

    def stop_experiment(self, save_data: bool = True, reason: str = "用户停止") -> dict:
        """
        停止实验
        
        Args:
            save_data: 是否保存数据
            reason: 停止原因
            
        Returns:
            实验结果
        """
        with self._lock:
            if not self._is_running:
                return {"success": False, "error": "实验未在运行"}
            
            self._is_running = False
        
        try:
            # 1. 停止数据采集
            data_count = 0
            if self.collector.is_collecting():
                data = self.collector.get_collected_data()
                data_count = len(data)
                self.collector.stop_collection()
            
            # 2. 准备保存数据
            result = {
                "success": True, 
                "data_points": data_count, 
                "reason": reason,
                "retry_requested": reason == "数据质量不合格，用户选择重新采集"
            }
            
            # 3. 保存数据
            should_save = save_data
            
            if should_save and data_count > 0:
                save_result = self.paradigm_service.save_results()
                if save_result and save_result.get("success"):
                    result.update(save_result)
                    print(f"✅ 实验数据已保存: {data_count} 个数据点")
                else:
                    result["success"] = False
                    result["error"] = "数据保存失败"
            elif should_save and data_count == 0:
                result["output_dir"] = "无数据"
                print("⚠️ 没有数据需要保存")
            elif not should_save and not result.get("retry_requested"):
                result["output_dir"] = "用户选择不保存"
                print("🗑️ 用户选择不保存数据")
            
            # 5. 清理资源
            self._cleanup()
            
            # 6. 调用回调
            if reason == "正常完成":
                if self.experiment_finished_callback:
                    self.experiment_finished_callback(result)
            else:
                if self.experiment_stopped_callback:
                    self.experiment_stopped_callback(result)
            
            return result
            
        except Exception as e:
            error_result = {"success": False, "error": f"停止实验失败: {e}"}
            if self.error_occurred_callback:
                self.error_occurred_callback(error_result["error"])
            return error_result

    def set_current_stimulus(self, index: int) -> bool:
        """
        设置当前刺激
        
        Args:
            index: 刺激索引
            
        Returns:
            是否成功设置
        """
        with self._lock:
            if not self._is_running or index >= len(self._stimuli):
                return False
            
            self._current_stimulus_index = index
        
        try:
            stimulus = self._stimuli[index]
            
            # 设置采集器的刺激上下文
            self.collector.set_current_stimulus(
                index=index,
                name=stimulus.path.name,
                path=str(stimulus.path),
                collect=getattr(stimulus, 'collect', True)
            )
            
            # 调用刺激变化回调
            stimulus_info = {
                "index": index,
                "name": stimulus.path.name,
                "path": str(stimulus.path),
                "collect": getattr(stimulus, 'collect', True),
                "duration": stimulus.duration,
                "total": len(self._stimuli)
            }
            if self.stimulus_changed_callback:
                self.stimulus_changed_callback(stimulus_info)
            
            return True
            
        except Exception as e:
            if self.error_occurred_callback:
                self.error_occurred_callback(f"设置刺激失败: {e}")
            return False

    def get_experiment_status(self) -> dict:
        """获取实验状态"""
        with self._lock:
            if not self._is_running:
                return {"is_running": False}
            
            data_count = len(self.collector.get_collected_data()) if self.collector else 0
            duration = time.time() - self._start_time if self._start_time else 0
            
            return {
                "is_running": True,
                "data_count": data_count,
                "duration": duration,
                "current_stimulus": self._current_stimulus_index,
                "total_stimuli": len(self._stimuli)
            }



    def _cleanup(self) -> None:
        """清理资源"""
        try:
            if self.collector:
                self.collector.clear_current_stimulus()
            
            if self.paradigm_service:
                self.paradigm_service.clear_session_data()
                
        except Exception as e:
            print(f"⚠️ 清理资源时出错: {e}")

    def is_running(self) -> bool:
        """检查实验是否在运行"""
        with self._lock:
            return self._is_running
