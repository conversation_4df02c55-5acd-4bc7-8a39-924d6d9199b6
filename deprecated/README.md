# 废弃文件夹 (Deprecated)

本文件夹包含在MVP架构改造过程中被替代的文件，保留用于历史参考。

## 改造日期
2025-08-16

## 废弃文件列表

### 1. experiment_controller.py
- **原路径**: `src/qyem_demo/experiment_controller.py`
- **替代者**: `src/qyem_demo/ui/presenters/experiment_presenter.py`
- **废弃原因**: 在MVP架构改造中，ExperimentController被重构为ExperimentPresenter，符合MVP模式的Presenter层设计

### 2. ui/viewmodels/ 目录
- **原路径**: `src/qyem_demo/ui/viewmodels/`
- **替代者**: `src/qyem_demo/ui/presenters/`
- **废弃原因**: MVVM架构的ViewModel层被重构为MVP架构的Presenter层

#### 具体文件映射:
- `viewmodels/home_viewmodel.py` → `presenters/home_presenter.py`
- `viewmodels/assessment_viewmodel.py` → `presenters/assessment_presenter.py`
- `viewmodels/__init__.py` → `presenters/__init__.py`

## 架构变更说明

### 改造前 (MVVM/MVC混合)
- **View**: HomePage, AssessmentPage
- **ViewModel**: HomeViewModel, AssessmentViewModel  
- **Controller**: ExperimentController
- **Model**: UserInfo, AssessmentData等

### 改造后 (MVP)
- **View**: HomePage, AssessmentPage (使用组合模式实现接口)
- **Presenter**: HomePresenter, AssessmentPresenter, ExperimentPresenter
- **Model**: UserInfo, AssessmentData等 (保持不变)

## 主要改进

1. **统一架构模式**: 从MVVM/MVC混合改为纯MVP架构
2. **避免多重继承**: 使用组合模式实现接口，避免QWidget和接口的多重继承问题
3. **更好的测试性**: Presenter层独立于UI框架，更容易进行单元测试
4. **清晰的职责分离**: View只负责UI，Presenter处理业务逻辑，Model管理数据

## 注意事项

- 这些文件已不再被项目使用，仅作历史参考保留
- 如需回滚或参考旧实现，可以查看这些文件
- 建议在确认新架构稳定后，可以考虑删除此文件夹
