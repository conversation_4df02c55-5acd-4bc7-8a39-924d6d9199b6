"""
模拟版 tobii_research

提供最小可用实现，支持本项目的数据采集与校准流程：
- 常量：EYETRACKER_GAZE_DATA 等
- find_all_eyetrackers()
- EyeTracker：subscribe_to / unsubscribe_from、频率设置、校准数据获取等

注意：该实现仅用于开发与测试环境，不代表真实 SDK 行为。
"""

from __future__ import annotations

import random
import threading
import time
from typing import Any, Callable, Optional

# 常量定义（与真实 SDK 名称保持一致）
EYETRACKER_GAZE_DATA = "gaze_data"
EYETRACKER_EYE_OPENNESS_DATA = "eye_openness_data"
EYETRACKER_NOTIFICATION_STREAM_BUFFER_OVERFLOW = "buffer_overflow"

# 校准状态常量
CALIBRATION_STATUS_SUCCESS = 0
CALIBRATION_STATUS_FAILURE = 1


def find_all_eyetrackers() -> list["EyeTracker"]:
    """返回一个模拟设备列表。"""
    return [
        EyeTracker(
            address="MOCK_ADDRESS_127.0.0.1",
            model="Tobii Pro Mock",
            device_name="Mock Eye Tracker",
            serial_number="MOCK-123456",
        )
    ]


class LicenseKey:
    def __init__(self, license_data: bytes) -> None:
        self.license_data = license_data
        self.validation_result = "OK"


class EyeTracker:
    """模拟眼动仪设备。"""

    def __init__(
        self,
        address: str,
        model: str = "Tobii Pro Mock",
        device_name: str = "Mock Eye Tracker",
        serial_number: str = "MOCK-000000",
    ) -> None:
        self.address = address
        self.model = model
        self.device_name = device_name
        self.serial_number = serial_number

        self._frequency_hz: float = 60.0
        self._stream_thread: Optional[threading.Thread] = None
        self._stop_event: Optional[threading.Event] = None
        self._callback: Optional[Callable[..., None]] = None
        self._data_type: Optional[str] = None

    # 许可证相关
    def apply_licenses(
        self, licenses: list[bytes] | list[LicenseKey] | bytes | LicenseKey
    ) -> list[LicenseKey]:
        # 简化实现：总是成功
        return []

    # 频率相关
    def get_all_gaze_output_frequencies(self) -> list[float]:
        return [30.0, 60.0, 120.0]

    def get_gaze_output_frequency(self) -> float:
        return self._frequency_hz

    def set_gaze_output_frequency(self, frequency: float) -> None:
        if frequency <= 0:
            raise ValueError("frequency must be positive")
        self._frequency_hz = frequency

    # 校准数据
    def retrieve_calibration_data(self) -> bytes | None:
        # 返回固定的模拟数据
        return b"MOCK_CALIBRATION_DATA"

    def apply_calibration_data(self, calibration_data: bytes) -> None:
        # 模拟应用成功
        return None

    # 数据订阅
    def subscribe_to(
        self, data_type: str, callback: Callable[..., None], **kwargs: Any
    ) -> None:
        if self._stream_thread is not None:
            # 已经在推送，忽略重复订阅
            return
        self._data_type = data_type
        self._callback = callback
        self._stop_event = threading.Event()
        self._stream_thread = threading.Thread(
            target=self._run_stream_loop, name="MockGazeStream", daemon=True
        )
        self._stream_thread.start()

    def unsubscribe_from(
        self, data_type: str, callback: Optional[Callable[..., None]] = None
    ) -> None:
        if self._stream_thread is None:
            return
        if self._stop_event is not None:
            self._stop_event.set()
        self._stream_thread.join(timeout=2.0)
        self._stream_thread = None
        self._stop_event = None
        self._callback = None
        self._data_type = None

    # 内部：数据推送循环
    def _run_stream_loop(self) -> None:
        assert self._stop_event is not None
        period = 1.0 / max(self._frequency_hz, 1.0)
        while not self._stop_event.is_set():
            if self._callback and self._data_type == EYETRACKER_GAZE_DATA:
                payload = self._generate_gaze_sample()
                try:
                    # as_dictionary=True 时，真实 SDK 会传 dict
                    self._callback(payload)
                except Exception:
                    # 模拟环境下忽略回调异常，避免线程崩溃
                    pass
            time.sleep(period)

    def _generate_gaze_sample(self) -> dict[str, Any]:
        # 随机生成 [0,1] 范围内的注视点和有效性标记
        def rand_point() -> tuple[float, float]:
            return (random.random(), random.random())

        left_valid = 1 if random.random() > 0.1 else 0
        right_valid = 1 if random.random() > 0.1 else 0

        now_ms = int(time.time() * 1000)
        return {
            "device_time_stamp": now_ms,
            "system_time_stamp": now_ms,
            "left_gaze_point_on_display_area": rand_point(),
            "right_gaze_point_on_display_area": rand_point(),
            "left_gaze_point_validity": left_valid,
            "right_gaze_point_validity": right_valid,
        }


class ScreenBasedCalibration:
    def __init__(self, eyetracker: EyeTracker) -> None:
        self._et = eyetracker
        self._entered = False

    def enter_calibration_mode(self) -> None:
        self._entered = True

    def leave_calibration_mode(self) -> None:
        self._entered = False

    def collect_data(self, x: float, y: float) -> int:
        if not self._entered:
            return CALIBRATION_STATUS_FAILURE
        return CALIBRATION_STATUS_SUCCESS

    def discard_data(self, x: float, y: float) -> None:
        return None

    def compute_and_apply(self) -> "CalibrationResult":
        return CalibrationResult(status=CALIBRATION_STATUS_SUCCESS, calibration_points=[])


class CalibrationResult:
    def __init__(self, status: int, calibration_points: list["CalibrationPoint"]) -> None:
        self.status = status
        self.calibration_points = calibration_points


class CalibrationPoint:
    def __init__(
        self,
        position_on_display_area: tuple[float, float],
        left_eye: Optional["CalibrationEyeData"],
        right_eye: Optional["CalibrationEyeData"],
    ) -> None:
        self.position_on_display_area = position_on_display_area
        self.left_eye = left_eye
        self.right_eye = right_eye


class CalibrationEyeData:
    def __init__(
        self,
        validity: int,
        position_on_display_area: tuple[float, float],
        position_in_user_coordinate_system: tuple[float, float, float],
    ) -> None:
        self.validity = validity
        self.position_on_display_area = position_on_display_area
        self.position_in_user_coordinate_system = position_in_user_coordinate_system


__all__ = [
    "EYETRACKER_GAZE_DATA",
    "EYETRACKER_EYE_OPENNESS_DATA",
    "EYETRACKER_NOTIFICATION_STREAM_BUFFER_OVERFLOW",
    "CALIBRATION_STATUS_SUCCESS",
    "CALIBRATION_STATUS_FAILURE",
    "find_all_eyetrackers",
    "EyeTracker",
    "LicenseKey",
    "ScreenBasedCalibration",
    "CalibrationResult",
    "CalibrationPoint",
    "CalibrationEyeData",
]
