# QYEM Demo - 眼动仪数据采集系统

一个简化、高效的眼动仪数据采集系统，提供直观的图形界面。

## 🚀 特性

- 🎨 **图形界面**：直观易用的GUI界面
- 🔧 **简化架构**：7个核心模块，易于维护
- 📊 **数据采集**：支持实时眼动数据采集和保存
- 🎬 **范式管理**：灵活的实验范式配置和执行
- 🖥️ **跨平台**：支持Windows、macOS和Linux
- 🎮 **演示模式**：无需硬件即可体验完整功能

## 📋 环境要求

- Python 3.10+
- uv (Python包管理器)
- PySide6 (Qt界面框架)
- tobii-research (可选，用于真实眼动仪)

## 🛠️ 安装

1. **克隆项目**：
```bash
git clone <repository-url>
cd qyem-demo
```

2. **安装依赖**：
```bash
uv sync
```

3. **激活环境**：
```bash
uv shell
```

## 🎯 快速开始

```bash
# 启动图形界面（推荐首次使用）
python -m qyem_demo

# 演示模式（不连接真实眼动仪）
python -m qyem_demo --dry-run

# 使用自定义配置
python -m qyem_demo --config config.toml

# 应用许可证
python -m qyem_demo --license license.bytes --config config.toml

# 或者使用专用GUI启动器
python gui_launcher.py                    # 正常模式
python gui_launcher.py --dry-run          # 演示模式
python gui_launcher.py --config my_config.toml  # 自定义配置
```

### 帮助信息

```bash
python -m qyem_demo --help
```

## 🏗️ 系统架构

### 简化后的核心架构

```
src/qyem_demo/
├── __init__.py
├── __main__.py              # 统一入口点
├── config.py                # 配置管理 (12.8KB)
├── eyetracker.py            # 眼动仪管理 (11.4KB)
├── collector.py             # 数据采集 (10.5KB)
├── paradigm.py              # 范式管理 (16.7KB)
├── display.py               # 显示管理 (7.9KB)
├── gui_app.py               # GUI应用 (7.5KB)
└── ui/                      # Qt界面组件
    ├── main_window.py
    └── pages/
        ├── home_page.py
        ├── playback_page.py
        └── base_page.py
```

### 设计原则

- **单一职责**：每个模块负责一个明确的功能域
- **最小依赖**：减少模块间的相互依赖
- **直接导入**：`from qyem_demo import config, eyetracker, collector`
- **功能聚合**：相关功能放在同一个文件中

## 📊 核心模块

### 配置管理 (`config.py`)
- 统一的TOML配置文件加载和验证
- 支持多种刺激组配置
- 配置值的获取和验证

### 眼动仪管理 (`eyetracker.py`)
- 设备查找和连接
- 许可证应用
- 校准程序启动
- 模拟模式支持

### 数据采集 (`collector.py`)
- 实时眼动数据采集
- 模拟数据生成
- CSV格式数据保存
- 线程安全操作

### 范式管理 (`paradigm.py`)
- 实验范式配置解析
- 刺激材料管理
- 会话执行控制
- 结果报告生成

### 显示管理 (`display.py`)
- Qt窗口管理
- 图片和消息显示
- 全屏模式支持
- 用户交互处理

## ⚙️ 配置文件

系统使用TOML格式的配置文件，支持灵活的实验配置：

```toml
# 配置描述
description = "眼动仪配置示例文件"

# 全局配置
[global]
output_dir = "./out"

# 设备配置
[device]
name = "IS510-100204006643"
license_file = "./licenses/license.bytes"

# 采集范式
[collection-paradigm]
subscriptions = ["gaze_data"]
stimulis_group = "group-default"

# 刺激组配置
[collection-paradigm.group-default]
stimulus_dir = "./images/group-default"
stimulis = [
    "image, 12s, calibration.png, true",      # 采集数据
    "image, 6s, stimulus1.jpg, true",         # 采集数据
    "image, 6s, stimulus2.jpg, false",        # 不采集数据
]

# 眼动仪配置
[tracker_config]
gaze_output_frequency = 60.0
```

### 刺激组优势

- **模块化管理**：不同实验使用不同刺激组
- **易于切换**：修改`stimulis_group`快速切换
- **配置复用**：多实验共享刺激组配置
- **采集控制**：每个刺激可独立控制是否采集数据

### 刺激采集控制

每个刺激项现在支持第四个参数来控制是否在播放该刺激时采集眼动数据：

```toml
stimulis = [
    "image, 6s, practice.jpg, false",     # 练习阶段，不采集
    "image, 6s, experiment.jpg, true",    # 正式实验，采集数据
    "image, 3s, rest.jpg, false",         # 休息图片，不采集
]
```

**应用场景：**
- 练习阶段设为 `false`，让被试熟悉实验
- 休息图片设为 `false`，避免采集无效数据
- 校准验证图片设为 `true`，检查校准效果
- 正式实验刺激设为 `true`，采集有效数据
- **结构清晰**：按组分类，便于管理

## 🔧 开发

### 代码风格

```bash
# 格式化代码
uv run black src/ tests/
uv run isort src/ tests/

# 代码检查
uv run flake8 src/ tests/
uv run mypy src/
```

### 运行测试

```bash
uv run pytest
```

### 构建发布

```bash
# 构建项目
uv run hatch build

# 发布项目
uv run hatch publish
```

## 📖 使用示例

### 基本数据采集

```python
from qyem_demo.eyetracker import EyeTracker
from qyem_demo.collector import DataCollector

# 连接眼动仪
tracker = EyeTracker(dry_run=True)  # 演示模式
tracker.connect()

# 数据采集
collector = DataCollector(tracker.eyetracker)
data = collector.collect_for_duration(5.0)  # 采集5秒
collector.save_data(data, "output.csv")
```

### 范式实验

```python
from qyem_demo.config import Config
from qyem_demo.paradigm import Paradigm

# 加载配置
config = Config()
config_data = config.load_config("config.toml")

# 创建和运行实验
paradigm = Paradigm(config, tracker, collector, display)
session = paradigm.create_session_from_config("config.toml", "experiment_1")
result = paradigm.run_paradigm("experiment_1")
```

## 🎯 架构优势

### 简化效果

- **文件数量**：从20+个文件减少到**7个核心模块**
- **代码行数**：减少约**30-40%**
- **目录层次**：从4层简化到**2层**
- **导入复杂度**：大幅简化

### 开发体验

- **导入简化**：`from qyem_demo import config, eyetracker`
- **结构清晰**：文件目录一目了然
- **调试方便**：问题定位更容易
- **维护简单**：相关功能集中管理

### 用户体验

- **启动更快**：减少模块加载时间
- **功能完整**：保持所有原有功能
- **错误更清晰**：简化的调用栈

## 📚 文档

- [架构设计文档](ARCHITECTURE_SIMPLIFIED.md) - 详细的架构设计说明
- [配置文件示例](config.toml.example) - 完整的配置文件模板

## 🤝 贡献

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目使用 [MIT 许可证](LICENSE.txt)。

## 🆘 故障排除

### 常见问题

**Q: 提示"tobii_research 库未安装"**
A: 这是正常的，系统会自动使用模拟模式。如需连接真实眼动仪，请安装：
```bash
pip install tobii-research
```

**Q: GUI模式无法启动**
A: 确保已安装PySide6：
```bash
uv add PySide6
```

**Q: 配置文件错误**
A: 检查TOML语法，参考`config.toml.example`

### 获取帮助

- 查看帮助：`python -m qyem_demo --help`
- 使用演示模式：`--dry-run`参数
- 检查日志输出中的错误信息

---

**眼动仪数据采集系统 - 简化、高效、易用** 🚀
