"""
Type stubs for tobii_research module.
This file provides type information for the Tobii Pro SDK Python bindings.
"""

from collections.abc import Callable
from typing import Any

# Constants
EYETRACKER_GAZE_DATA: str
EYETRACKER_EYE_OPENNESS_DATA: str
EYETRACKER_NOTIFICATION_STREAM_BUFFER_OVERFLOW: str

# Calibration constants
CALIBRATION_STATUS_SUCCESS: int
CALIBRATION_STATUS_FAILURE: int

def find_all_eyetrackers() -> list[EyeTracker]: ...

class EyeTracker:
    """Represents a Tobii eye tracker device."""

    address: str
    model: str
    device_name: str
    serial_number: str

    def __init__(self, address: str) -> None: ...
    def apply_licenses(
        self, licenses: list[bytes] | list[LicenseKey] | bytes | LicenseKey
    ) -> list[LicenseKey]: ...
    def get_all_gaze_output_frequencies(self) -> list[float]: ...
    def get_gaze_output_frequency(self) -> float: ...
    def set_gaze_output_frequency(self, frequency: float) -> None: ...
    def retrieve_calibration_data(self) -> bytes | None: ...
    def apply_calibration_data(self, calibration_data: bytes) -> None: ...
    def subscribe_to(
        self, data_type: str, callback: Callable[..., None], **kwargs: Any
    ) -> None: ...
    def unsubscribe_from(
        self, data_type: str, callback: Callable[..., None] | None = None
    ) -> None: ...

class LicenseKey:
    """Represents a license key for the eye tracker."""

    def __init__(self, license_data: bytes) -> None: ...
    validation_result: str

class ScreenBasedCalibration:
    """Handles screen-based calibration for eye trackers."""

    def __init__(self, eyetracker: EyeTracker) -> None: ...
    def enter_calibration_mode(self) -> None: ...
    def leave_calibration_mode(self) -> None: ...
    def collect_data(self, x: float, y: float) -> int: ...
    def discard_data(self, x: float, y: float) -> None: ...
    def compute_and_apply(self) -> CalibrationResult: ...

class CalibrationResult:
    """Represents the result of a calibration operation."""

    status: int
    calibration_points: list[CalibrationPoint]

class CalibrationPoint:
    """Represents a calibration point."""

    position_on_display_area: tuple[float, float]
    left_eye: CalibrationEyeData | None
    right_eye: CalibrationEyeData | None

class CalibrationEyeData:
    """Represents calibration data for one eye."""

    validity: int
    position_on_display_area: tuple[float, float]
    position_in_user_coordinate_system: tuple[float, float, float]
