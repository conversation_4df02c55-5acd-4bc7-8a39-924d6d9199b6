"""
范式服务 - 实验流程控制和执行
"""

import time
from pathlib import Path
from typing import Any, cast
import csv

from .collector import DataCollector
from .config import Config


class StimulusItem:
    """刺激项"""

    def __init__(
        self,
        stimulus_type: str,
        duration: float,
        path: Path,
        collect: bool = True,
        **kwargs: Any,
    ):
        """
        初始化刺激项

        Args:
            stimulus_type: 刺激类型（如 "image"）
            duration: 刺激持续时间（秒）
            path: 刺激文件路径
            collect: 是否在播放该刺激时采集数据
            **kwargs: 其他参数
        """
        self.stimulus_type = stimulus_type
        self.duration = duration
        self.path = path
        self.collect = collect
        self.extra_params = kwargs

        # 验证文件是否存在
        if not self.path.exists():
            print(f"⚠️ 刺激文件不存在: {self.path}")

    def __str__(self) -> str:
        collect_status = "采集" if self.collect else "不采集"
        return f"Stimulus({self.stimulus_type}, {self.duration}s, {self.path.name}, {collect_status})"


class ParadigmSession:
    """范式会话"""

    def __init__(
        self,
        name: str,
        description: str = "",
        output_dir: str = "",
        metadata: dict[str, Any] | None = None,
        user_info: dict[str, Any] | None = None,
    ):
        """
        初始化范式会话

        Args:
            name: 会话名称
            description: 会话描述
            output_dir: 输出目录
            metadata: 元数据
            user_info: 受试者信息
        """
        self.name = name
        self.description = description
        self.output_dir = output_dir or f"out/{name}"
        self.metadata = metadata or {}
        self.user_info = user_info or {}
        self.stimuli: list[StimulusItem] = []
        self.created_at = time.time()
        self.status = "created"  # created, running, finished, cancelled

    def add_stimulus(self, stimulus: StimulusItem) -> None:
        """添加刺激项"""
        self.stimuli.append(stimulus)

    def get_total_duration(self) -> float:
        """获取总持续时间"""
        return sum(stimulus.duration for stimulus in self.stimuli)
    
    def get_collect_duration(self) -> float:
        """获取采集总时长"""
        return sum(stimulus.duration for stimulus in self.stimuli if stimulus.collect)

    def validate(self) -> list[str]:
        """
        验证会话有效性

        Returns:
            错误信息列表
        """
        errors = []
        if not self.stimuli:
            errors.append("会话中没有刺激项")

        for i, stimulus in enumerate(self.stimuli):
            if not stimulus.path.exists():
                errors.append(f"刺激 {i + 1} 文件不存在: {stimulus.path}")
            if stimulus.duration <= 0:
                errors.append(f"刺激 {i + 1} 持续时间无效: {stimulus.duration}")
        return errors


class ParadigmService:
    """
    范式服务
    - 从配置加载范式定义
    - 控制数据采集生命周期
    - 保存眼动结果
    """

    def __init__(self, config: Config, collector: DataCollector):
        """
        初始化范式服务

        Args:
            config: 配置管理器
            collector: 数据采集器
        """
        self.config = config
        self.collector = collector
        self.current_session: ParadigmSession | None = None
        print("🎬 范式服务初始化完成")

    def create_session(self, session_name: str | None = None, user_info: dict | None = None) -> ParadigmSession | None:
        """
        根据当前配置创建新的范式会话

        Args:
            session_name: 会话名称，如果为None则自动生成
            user_info: 受试者信息

        Returns:
            创建的会话，失败时返回None
        """
        config_data = self.config.get_config()
        if not config_data:
            print("❌ 配置未加载")
            return None

        # 验证配置
        errors = self.config.validate_config(config_data)
        if errors:
            print("❌ 配置验证失败:")
            for error in errors:
                print(f"  - {error}")
            return None

        if not session_name:
            session_name = f"session_{int(time.time())}"

        # 获取刺激组和输出目录
        stimuli_group = self.config.get_stimuli_group()
        output_dir = self.config.get_output_dir()
        session_output_path = str(Path(output_dir) / session_name)

        # 创建会话
        session = ParadigmSession(
            name=session_name,
            description=config_data.get("description", ""),
            output_dir=session_output_path,
            metadata={
                "config_path": self.config._config_path,
                "stimuli_group": stimuli_group,
                "subscriptions": self.config.get_subscriptions(),
            },
            user_info=user_info,
        )

        # 解析刺激配置
        self._parse_stimuli_config(session, config_data, stimuli_group)

        # 验证会话
        session_errors = session.validate()
        if session_errors:
            print(f"❌ 会话 '{session_name}' 验证失败:")
            for error in session_errors:
                print(f"  - {error}")
            return None

        print(f"✅ 创建范式会话: {session_name}")
        print(f"   - 刺激数量: {len(session.stimuli)}")
        print(f"   - 总时长: {session.get_total_duration():.1f}秒")
        print(f"   - 输出目录: {session.output_dir}")

        self.current_session = session
        return session

    def get_stimuli(self) -> list:
        """获取当前会话的刺激列表"""
        if not self.current_session:
            return []
        return self.current_session.stimuli

    def get_session_info(self, session_name: str, user_info: dict) -> dict:
        """获取会话信息"""
        return {
            "session_name": session_name,
            "user_info": user_info,
            "config": self.config.dict() if self.config else {}
        }

    def save_results(self, data: list[dict[str, Any]] = None) -> dict[str, Any] | None:
        """
        保存会话数据和报告

        Args:
            data: 要保存的数据，如果为None则从collector获取

        Returns:
            保存结果的字典，失败时返回None
        """
        if not self.current_session:
            print("❌ 没有会话数据可保存")
            return None

        session = self.current_session

        # 如果没有提供数据，从collector获取
        if data is None:
            data = self.collector.get_collected_data()

        print(f"💾 正在保存会话 '{session.name}' 的结果...")

        try:
            # 确保输出目录存在
            output_dir = Path(session.output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)

            # 1. 保存原始数据
            data_filename = f"{session.name}_data.csv"
            data_filepath = output_dir / data_filename
            self.collector.save_data(data, str(data_filepath))

            # 2. 保存会话报告
            report_filename = f"{session.name}_report.txt"
            report_filepath = output_dir / report_filename
            self._save_text_report(session, data, str(report_filepath))

            # 3. 保存受试者信息为 subject_info.csv
            if session.user_info:
                subject_info_path = output_dir / f"{session.name}_subject_info.csv"
                with open(subject_info_path, "w", newline="", encoding="utf-8") as f:
                    writer = csv.DictWriter(f, fieldnames=["session_name"] + list(session.user_info.keys()))
                    writer.writeheader()
                    writer.writerow({"session_name": session.name, **session.user_info})
                print(f"✅ 受试者信息已保存到: {subject_info_path}")

            result = {
                "success": True,
                "data_file": str(data_filepath),
                "report_file": str(report_filepath),
                "subject_info_file": str(subject_info_path) if session.user_info else None,
                "data_points": len(data),
                "session_duration": session.get_total_duration(),
                "collect_duration": session.get_collect_duration(),
                "output_dir": str(output_dir),
            }
            print(f"✅ 结果已保存到: {output_dir}")
            return result

        except Exception as e:
            error_msg = f"保存会话数据失败: {e}"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}
        finally:
            # 注释掉自动清空数据，让用户手动决定何时清空
            # self.collector.clear_data()
            self.current_session = None

    def clear_session_data(self) -> None:
        """手动清空会话数据"""
        if self.collector:
            self.collector.clear_data()
            print("🗑️ 已手动清空会话数据")

    def _parse_stimuli_config(
        self, session: ParadigmSession, config_data: dict[str, Any], stimuli_group: str
    ) -> None:
        """
        解析刺激配置

        Args:
            session: 会话对象
            config_data: 配置数据
            stimuli_group: 刺激组名称
        """
        paradigm_config = cast(
            dict[str, Any], config_data.get("collection-paradigm", {})
        )
        group_config = cast(dict[str, Any], paradigm_config.get(stimuli_group, {}))

        if not group_config:
            print(f"⚠️ 未找到刺激组配置: {stimuli_group}")
            return

        stimulus_dir_str = group_config.get("stimulus_dir", "")
        stimulus_dir = Path(stimulus_dir_str) if stimulus_dir_str else None

        # 在旧版配置中，键是 stimulis，为了兼容性同时检查 stimuli
        stimuli_list = group_config.get("stimulis", group_config.get("stimuli"))
        if not stimuli_list:
            print(f"⚠️ 刺激组 '{stimuli_group}' 中没有找到 'stimulis' 或 'stimuli' 配置")
            return

        for stimulus_config in stimuli_list:
            try:
                # 假设配置是 "type, duration, path" 或 "type, duration, path, collect" 格式的字符串
                if isinstance(stimulus_config, str):
                    parts = [part.strip() for part in stimulus_config.split(",")]
                    if len(parts) >= 3:
                        stimulus_type, duration_str, filename = parts[:3]
                        duration = self._parse_duration(duration_str)
                        
                        # 解析是否采集字段（第四个参数，可选）
                        collect = True  # 默认值
                        if len(parts) >= 4:
                            collect_str = parts[3].strip().lower()
                            collect = collect_str in ("true", "1", "yes", "是")

                        # 构建文件路径
                        stimulus_path = (
                            stimulus_dir / filename if stimulus_dir else Path(filename)
                        )

                        stimulus = StimulusItem(
                            stimulus_type=stimulus_type,
                            duration=duration,
                            path=stimulus_path,
                            collect=collect,
                        )
                        session.add_stimulus(stimulus)
                    else:
                        print(f"⚠️ 无效的刺激格式: {stimulus_config}")

            except Exception as e:
                print(f"❌ 解析刺激配置失败: {stimulus_config}, 错误: {e}")

    def _parse_duration(self, duration_str: str) -> float:
        """
        解析持续时间字符串 (例如 "2s", "1.5s", "300ms")

        Returns:
            持续时间（秒）
        """
        duration_str = duration_str.strip().lower()
        if duration_str.endswith("ms"):
            return float(duration_str[:-2]) / 1000.0
        if duration_str.endswith("s"):
            return float(duration_str[:-1])
        # 默认假设是秒
        return float(duration_str)

    def _save_text_report(
        self, session: ParadigmSession, data: list[dict[str, Any]], report_path: str
    ) -> None:
        """
        保存文本格式的会话报告

        Args:
            session: 会话对象
            data: 采集的数据
            report_path: 报告文件路径
        """
        try:
            with open(report_path, "w", encoding="utf-8") as f:
                f.write("# 眼动仪数据采集报告\n\n")
                f.write(f"会话名称: {session.name}\n")
                f.write(f"会话状态: {session.status}\n")
                f.write(
                    f"创建时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(session.created_at))}\n"
                )
                f.write(f"总刺激数: {len(session.stimuli)}\n")
                f.write(f"理论总时长: {session.get_total_duration():.1f} 秒\n")
                f.write(f"采集总时长: {session.get_collect_duration():.1f} 秒\n")
                f.write(f"采集数据点: {len(data)}\n")
                f.write(f"报告生成于: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                f.write("## 刺激列表\n")
                f.write("```\n")
                for i, stimulus in enumerate(session.stimuli):
                    f.write(
                        f"{i + 1:02d}. 类型: {stimulus.stimulus_type}, "
                        f"时长: {stimulus.duration}s, "
                        f"文件: {stimulus.path.name}, "
                        f"采集: {'是' if stimulus.collect else '否'}\n"
                    )
                f.write("```\n\n")

                f.write("## 元数据\n")
                f.write("```\n")
                for key, value in session.metadata.items():
                    f.write(f"{key}: {value}\n")
                f.write("```\n")

            print(f"✅ 报告已保存: {report_path}")

        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
