"""
数据质量检查器 - GUI模式专用
提供数据质量检查和Qt对话框交互功能
"""

from typing import Any, Dict
from PySide6.QtWidgets import QMessageBox


def show_quality_dialog(quality_result: Dict[str, Any]) -> bool:
    """
    显示数据质量警告对话框（仅数据不合格时显示）
    
    Args:
        quality_result: 数据质量检查结果
    
    Returns:
        True: 用户选择继续保存
        False: 用户选择重新采集
    """
    
    # 数据质量合格时直接返回True（继续保存）
    if quality_result["valid"]:
        return True
    
    # 数据质量不合格，显示明显的警告对话框
    warning_text = "⚠️ 数据质量不合格！"
    if quality_result["left_invalid_ratio"] >= 0.3:
        warning_text += f"\n\n❌ 左眼无效数据比例过高: {quality_result['left_invalid_ratio']:.1%}"
    if quality_result["right_invalid_ratio"] >= 0.3:
        warning_text += f"\n\n❌ 右眼无效数据比例过高: {quality_result['right_invalid_ratio']:.1%}"
    
    detailed_text = (
        f"📊 数据详情:\n"
        f"   总数据点数: {quality_result['total_count']}\n"
        f"\n👁️ 左眼数据:\n"
        f"   ✅ 有效: {quality_result['left_valid_count']} 个\n"
        f"   ❌ 无效: {quality_result['left_invalid_count']} 个\n"
        f"   📈 无效比例: {quality_result['left_invalid_ratio']:.1%}\n"
        f"\n👁️ 右眼数据:\n"
        f"   ✅ 有效: {quality_result['right_valid_count']} 个\n"
        f"   ❌ 无效: {quality_result['right_invalid_count']} 个\n"
        f"   📈 无效比例: {quality_result['right_invalid_ratio']:.1%}\n\n"
        f"⚡ 建议: 无效数据比例超过30%，可能影响分析结果\n\n"
        f"💾 是否继续保存当前数据？"
    )
    
    msg_box = QMessageBox()
    msg_box.setWindowTitle("🚨 数据质量警告")
    msg_box.setIcon(QMessageBox.Critical)  # 使用更明显的图标
    msg_box.setText(warning_text)
    msg_box.setDetailedText(detailed_text)
    
    # 设置按钮样式
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    msg_box.setDefaultButton(QMessageBox.No)  # 默认选择重新采集
    msg_box.setButtonText(QMessageBox.Yes, "💾 继续保存")
    msg_box.setButtonText(QMessageBox.No, "🔄 重新采集")
    
    choice = msg_box.exec()
    
    return choice == QMessageBox.Yes  # Yes表示继续保存，No表示重新采集


def show_data_collection_dialog(data_count: int, quality_result: Dict[str, Any] = None) -> bool:
    """
    统一的数据采集结果对话框
    
    Args:
        data_count: 采集到的数据点数量
        quality_result: 数据质量检查结果（可选）
    
    Returns:
        True: 用户选择继续保存
        False: 用户选择重新采集
    """
    
    if data_count == 0:
        # 未采集到数据
        msg_box = QMessageBox()
        msg_box.setWindowTitle("🚨 数据采集结果")
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setText("未采集到任何数据")
        msg_box.setInformativeText("是否重新采集数据？")
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        msg_box.setDefaultButton(QMessageBox.Yes)
        msg_box.setButtonText(QMessageBox.Yes, "🔄 重新采集")
        msg_box.setButtonText(QMessageBox.No, "⏭️ 不重新采集")
        
        choice = msg_box.exec()
        return choice == QMessageBox.Yes  # Yes表示重新采集，No表示不重新采集
    
    elif quality_result and not quality_result["valid"]:
        # 数据质量不合格
        return show_quality_dialog(quality_result)
    
    else:
        # 数据质量合格
        return True


def format_quality_report(quality_result: Dict[str, Any]) -> str:
    """
    格式化数据质量报告
    
    Args:
        quality_result: 数据质量检查结果
    
    Returns:
        格式化的报告文本
    """
    if quality_result["total_count"] == 0:
        return "没有采集到数据"
    
    report = f"""
数据质量检查报告
================
总数据点数: {quality_result['total_count']}

左眼数据:
  - 有效点数: {quality_result['left_valid_count']}
  - 无效点数: {quality_result['left_invalid_count']}
  - 无效比例: {quality_result['left_invalid_ratio']:.1%}

右眼数据:
  - 有效点数: {quality_result['right_valid_count']}
  - 无效点数: {quality_result['right_invalid_count']}
  - 无效比例: {quality_result['right_invalid_ratio']:.1%}
get_quality_warning_message
检查结果: {quality_result['message']}
"""
    
    return report.strip()


def get_quality_warning_message(quality_result: Dict[str, Any]) -> str:
    """
    获取质量警告消息
    
    Args:
        quality_result: 数据质量检查结果
    
    Returns:
        警告消息文本
    """
    if quality_result["valid"]:
        return "数据质量合格"
    
    messages = []
    
    if quality_result["left_invalid_ratio"] >= 0.3:
        messages.append(f"左眼无效数据比例过高 ({quality_result['left_invalid_ratio']:.1%})")
    
    if quality_result["right_invalid_ratio"] >= 0.3:
        messages.append(f"右眼无效数据比例过高 ({quality_result['right_invalid_ratio']:.1%})")
    
    return "; ".join(messages)