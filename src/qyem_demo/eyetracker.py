"""
眼动仪管理 - 设备连接、校准和控制
"""

import glob
import os
import platform
import subprocess
from typing import TYPE_CHECKING, Any, cast

if TYPE_CHECKING:
    from tobii_research import EyeTracker

try:
    import tobii_research as tr
    TR_AVAILABLE = True
except ImportError:
    tr = None  # type: ignore
    TR_AVAILABLE = False
    print("❌ tobii_research 库未安装，无法使用眼动仪")


class EyeTrackerSession:
    """眼动仪会话 - 管理单个眼动仪的完整使用周期"""

    def __init__(self) -> None:
        """初始化眼动仪会话"""
        if not TR_AVAILABLE:
            raise ImportError("tobii_research 库未安装，无法使用眼动仪")

        self.eyetracker: EyeTracker | None = None
        self._device_info: dict[str, Any] = {}
        print("📊 眼动仪会话已开始")

    @staticmethod
    def find_devices() -> list["EyeTracker"]:
        """
        查找可用的眼动仪设备

        Returns:
            眼动仪设备列表
        """
        try:
            found_eyetrackers = tr.find_all_eyetrackers()
            if not found_eyetrackers:
                print("❌ 未找到眼动仪设备")
                return []

            print(f"✅ 找到 {len(found_eyetrackers)} 个眼动仪设备:")
            for i, tracker in enumerate(found_eyetrackers):
                print(f"  {i + 1}. {tracker.device_name} ({tracker.model})")
                print(f"     地址: {tracker.address}")
                print(f"     序列号: {tracker.serial_number}")

            return found_eyetrackers

        except Exception as e:
            print(f"❌ 查找眼动仪失败: {e}")
            return []

    def apply_eyetracker(self, device_index: int = 0) -> bool:
        """
        指定眼动仪

        Args:
            device_index: 设备索引，默认指定第一个设备

        Returns:
            是否指定成功
        """
        devices = EyeTrackerSession.find_devices()
        if not devices or device_index >= len(devices):
            print(f"❌ 设备索引 {device_index} 无效")
            return False

        self.eyetracker = devices[device_index]
        self._device_info = {
            "device_name": self.eyetracker.device_name,
            "model": self.eyetracker.model,
            "serial_number": self.eyetracker.serial_number,
            "address": self.eyetracker.address,
        }
        print(f"✅ 已指定眼动仪: {self._device_info['device_name']}")
        return True

    def clear_eyetracker(self) -> None:
        """清除眼动仪指定"""
        self.eyetracker = None
        self._device_info = {}
        print("✅ 已清除眼动仪指定")

    def has_device(self) -> bool:
        """
        检查是否已指定设备

        Returns:
            是否已指定设备
        """
        return self.eyetracker is not None

    def get_device_info(self) -> dict[str, Any]:
        """
        获取设备信息

        Returns:
            设备信息字典
        """
        return self._device_info.copy()

    def get_device_name(self) -> str:
        """
        获取设备名称

        Returns:
            设备名称
        """
        return cast(str, self._device_info.get("device_name", "未知设备"))

    def apply_license(self, license_path: str) -> bool:
        """
        应用眼动仪许可证

        Args:
            license_path: 许可证文件路径

        Returns:
            是否应用成功
        """
        if not self.eyetracker:
            print("❌ 未指定眼动仪设备，无法应用许可证")
            return False

        try:
            with open(license_path, "rb") as f:
                license_key = f.read()

            failed_licenses = self.eyetracker.apply_licenses([license_key])
            if not failed_licenses:
                print("✅ 许可证应用成功")
                return True

            for license in failed_licenses:
                print(f"❌ 许可证应用失败: {license.validation_result}")
            return False

        except FileNotFoundError:
            print(f"❌ 许可证文件不存在: {license_path}")
            return False
        except Exception as e:
            print(f"❌ 许可证应用失败: {e}")
            return False

    def start_calibration(self) -> bool:
        """
        启动校准程序

        Returns:
            是否启动成功
        """
        if not self.eyetracker:
            print("❌ 未指定眼动仪设备，无法启动校准")
            return False

        print("\n--- 启动校准程序 ---")
        try:
            etm_path = self._find_etm_path(platform.system())
            if not etm_path:
                print("⚠️ 未找到Tobii校准程序，请手动运行校准")
                return False

            print("正在启动眼动仪管理器，请完成校准后关闭窗口...")
            etm_p: subprocess.Popen = subprocess.Popen(
                [
                    etm_path,
                    f"--device-address={self.eyetracker.address}",
                    "--mode=usercalibration",
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=False,
            )
            stdout, stderr = etm_p.communicate()

            if etm_p.returncode == 0:
                print("校准过程成功完成")
                self._save_calibration_data()
                return True
            else:
                print(f"眼动仪管理器退出，代码: {etm_p.returncode}")
                error_log = (stdout or stderr).decode("utf-8", errors="ignore")
                if error_log:
                    print(f"错误日志: {error_log}")
                return False

        except Exception as e:
            print(f"校准过程中发生错误: {e}")
            return False
        finally:
            print("--- 校准完成 ---\n")

    def _save_calibration_data(self) -> None:
        """获取并保存校准数据"""
        if not self.eyetracker:
            return

        print("正在获取校准数据...")
        calibration_data = self.eyetracker.retrieve_calibration_data()
        if calibration_data:
            try:
                with open("calibration.bin", "wb") as f:
                    f.write(calibration_data)
                print("校准数据已成功保存到 calibration.bin")
            except OSError as e:
                print(f"保存校准数据失败: {e}")
        else:
            print("无法获取校准数据")

    def get_gaze_output_frequency(self) -> float:
        """
        获取眼动数据输出频率

        Returns:
            输出频率(Hz)
        """
        if not self.eyetracker:
            return 0.0
        try:
            return float(self.eyetracker.get_gaze_output_frequency())
        except Exception as e:
            print(f"❌ 获取输出频率失败: {e}")
            return 0.0

    def set_gaze_output_frequency(self, frequency: float) -> bool:
        """
        设置眼动数据输出频率

        Args:
            frequency: 目标频率(Hz)

        Returns:
            是否设置成功
        """
        if not self.eyetracker:
            print("❌ 未指定眼动仪设备，无法设置频率")
            return False

        try:
            self.eyetracker.set_gaze_output_frequency(frequency)
            print(f"✅ 输出频率已设置为 {frequency}Hz")
            return True
        except Exception as e:
            print(f"❌ 设置输出频率失败: {e}")
            return False

    def get_status(self) -> dict[str, Any]:
        """
        获取眼动仪状态

        Returns:
            状态信息字典
        """
        return {
            "has_device": self.has_device(),
            "device_info": self._device_info,
            "frequency": self.get_gaze_output_frequency() if self.has_device() else 0.0,
        }

    @staticmethod
    def _find_etm_path(os_type: str) -> str | None:
        """查找眼动仪管理器可执行文件的路径"""
        if os_type == "Windows":
            local_app_data = os.getenv("LocalAppData")
            if local_app_data:
                pattern = os.path.join(
                    local_app_data,
                    "Programs",
                    "TobiiProEyeTrackerManager",
                    "TobiiProEyeTrackerManager.exe",
                )
                paths = glob.glob(pattern)
                if paths:
                    return paths[0]
        elif os_type == "Linux":
            return "TobiiProEyeTrackerManager"
        elif os_type == "Darwin":
            return "/Applications/TobiiProEyeTrackerManager.app/Contents/MacOS/TobiiProEyeTrackerManager"
        return None
