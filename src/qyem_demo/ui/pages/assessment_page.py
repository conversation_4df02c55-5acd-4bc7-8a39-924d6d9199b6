"""
评估页面 - MVP架构的View层
"""

from typing import Dict, Optional, TYPE_CHECKING
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
from PySide6.QtWidgets import (
    QFrame,
    QLabel,
    QLineEdit,
    QPushButton,
    QVBoxLayout,
    QHBoxLayout,
    QWidget,
    QGridLayout,
    QScrollArea,
    QMessageBox,
    QGroupBox,
)

from ..interfaces.view_interfaces import IAssessmentView

if TYPE_CHECKING:
    from ..interfaces.presenter_interfaces import IAssessmentPresenter


class AssessmentViewImpl(IAssessmentView):
    """AssessmentPage的View接口实现 - 使用组合模式避免多重继承"""

    def __init__(self, assessment_page: "AssessmentPage"):
        self.assessment_page = assessment_page

    def show_error(self, message: str) -> None:
        """显示错误消息"""
        self.assessment_page.show_error(message)

    def show_success(self, message: str) -> None:
        """显示成功消息"""
        print(f"✅ {message}")

    def get_mmse_data(self) -> Dict[str, int | None]:
        """获取MMSE评估数据"""
        return self.assessment_page.get_mmse_data()

    def get_moca_data(self) -> Dict[str, int | None]:
        """获取MoCA评估数据"""
        return self.assessment_page.get_moca_data()

    def clear_forms(self) -> None:
        """清空所有表单"""
        self.assessment_page.clear_forms()

    def restore_forms(self, assessment_data: Dict[str, any]) -> None:
        """恢复表单数据"""
        self.assessment_page.restore_forms(assessment_data)

    def set_forms_enabled(self, enabled: bool) -> None:
        """设置表单是否可用"""
        self.assessment_page.set_forms_enabled(enabled)


class AssessmentPage(QWidget):
    """评估页面 - MVP架构的View层"""

    def __init__(
        self, parent: QWidget | None = None, presenter: Optional["IAssessmentPresenter"] = None
    ) -> None:
        """
        初始化评估页面

        Args:
            parent: 父组件
            presenter: Presenter实例，用于业务逻辑处理
        """
        super().__init__(parent)

        # 使用组合模式实现接口
        self.view_impl = AssessmentViewImpl(self)

        # 保存Presenter引用
        self.presenter = presenter
        if self.presenter:
            self.presenter.set_view(self.view_impl)

        # 设置基本属性
        self.setStyleSheet("background-color: #fafafa;")

        # 输入控件引用
        self.mmse_inputs: Dict[str, QLineEdit] = {}
        self.moca_inputs: Dict[str, QLineEdit] = {}

        # 评估项目配置 - 由ViewModel提供
        self.mmse_items = [
            {"name": "orientation", "label": "定向力", "max_score": 10},
            {"name": "instant_memory", "label": "瞬时记忆", "max_score": 3},
            {"name": "calculation", "label": "计算力", "max_score": 5},
            {"name": "short_term_memory", "label": "短时记忆", "max_score": 3},
            {"name": "naming", "label": "命名", "max_score": 2, "group": "语言能力"},
            {"name": "repetition", "label": "复述", "max_score": 1, "group": "语言能力"},
            {"name": "reading", "label": "阅读", "max_score": 1, "group": "语言能力"},
            {"name": "execution", "label": "执行", "max_score": 3, "group": "语言能力"},
            {"name": "writing", "label": "书写", "max_score": 1, "group": "语言能力"},
            {"name": "structure_imitation", "label": "结构模仿", "max_score": 1, "group": "语言能力"},
        ]

        self.moca_items = [
            {"name": "visuospatial_execution", "label": "视空间/执行", "max_score": 2},
            {"name": "clock_drawing", "label": "画钟", "max_score": 3},
            {"name": "naming", "label": "命名", "max_score": 3},
            {"name": "attention", "label": "注意力", "max_score": 3},
            {"name": "calculation", "label": "计算力", "max_score": 3},
            {"name": "language", "label": "语言", "max_score": 3},
            {"name": "abstraction", "label": "抽象能力", "max_score": 2},
            {"name": "delayed_recall", "label": "延迟记忆", "max_score": 5},
            {"name": "orientation", "label": "定向力", "max_score": 6},
        ]

        # 创建界面
        self._init_ui()

    def _init_ui(self) -> None:
        """初始化用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 标题
        title = QLabel("评估量表")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        title.setStyleSheet("color: #333;")
        main_layout.addWidget(title)

        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #fafafa;
            }
            QScrollBar:vertical {
                border: none;
                background-color: #f0f0f0;
                width: 10px;
            }
            QScrollBar::handle:vertical {
                background-color: #ccc;
                border-radius: 5px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #999;
            }
        """)

        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(30)

        # MMSE评估区域
        mmse_section = self._create_mmse_section()
        content_layout.addWidget(mmse_section)

        # MoCA评估区域
        moca_section = self._create_moca_section()
        content_layout.addWidget(moca_section)

        # 底部按钮区域
        button_section = self._create_button_section()
        content_layout.addWidget(button_section)

        # 设置滚动区域内容
        scroll.setWidget(content_widget)
        main_layout.addWidget(scroll)

    def _create_mmse_section(self) -> QWidget:
        """创建MMSE评估区域"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
            }
            QFrame::title {
                border: none;
                padding: 0px;
                color: #333;
                margin: 0px;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setSpacing(0)

        # 标题
        title = QLabel("MMSE (简易智能状态检查)")
        title.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        title.setStyleSheet(
            "color: #333; margin-bottom: 15px;border: none;padding: 0px;"
        )
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 创建网格布局容器
        container = QWidget()
        container.setStyleSheet("background-color: white;")
        container_layout = QGridLayout(container)
        container_layout.setHorizontalSpacing(20)
        container_layout.setVerticalSpacing(10)
        container_layout.setContentsMargins(0, 0, 0, 0)

        # 设置列宽比例，让两列占满宽度
        container_layout.setColumnStretch(0, 1)  # 第一列
        container_layout.setColumnStretch(1, 1)  # 第二列

        # 分离语言能力项目和其他项目
        language_items = []
        other_items = []

        for item_data in self.mmse_items:
            if item_data.get("group") == "语言能力":
                language_items.append(item_data)
            else:
                other_items.append(item_data)

        # 创建其他项目的输入字段
        current_row = 0
        for i, item_data in enumerate(other_items):
            item_name = item_data["name"]
            item_label = item_data["label"]
            max_score = item_data["max_score"]
            item_widget, score_input = self._create_score_input(item_label, max_score)
            self.mmse_inputs[item_name] = score_input

            # 计算网格位置：x行2列
            row = current_row + (i // 2)  # 每行2个项目
            col = i % 2  # 列位置
            container_layout.addWidget(item_widget, row, col)

        # 更新当前行位置
        current_row += (len(other_items) + 1) // 2

        # 创建语言能力分组
        if language_items:
            language_group = QGroupBox("语言能力")
            language_group.setStyleSheet("""
                QGroupBox {
                    font-weight: bold;
                    border: 1px solid #c0c0c0;
                    border-radius: 6px;
                    margin-top: 10px;
                    padding-top: 10px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px 0 5px;
                    color: #c0c0c0;
                }
            """)

            language_layout = QGridLayout(language_group)
            language_layout.setHorizontalSpacing(20)
            language_layout.setVerticalSpacing(10)
            language_layout.setContentsMargins(5, 5, 5, 10)

            # 设置语言能力组的列宽比例
            language_layout.setColumnStretch(0, 1)
            language_layout.setColumnStretch(1, 1)

            # 创建语言能力项目的输入字段
            for i, item_data in enumerate(language_items):
                item_name = item_data["name"]
                item_label = item_data["label"]
                max_score = item_data["max_score"]
                item_widget, score_input = self._create_score_input(item_label, max_score)
                self.mmse_inputs[item_name] = score_input

                # 计算网格位置：x行2列
                row = i // 2  # 每行2个项目
                col = i % 2  # 列位置
                language_layout.addWidget(item_widget, row, col)

            # 将语言能力组添加到主容器，跨越两列
            container_layout.addWidget(language_group, current_row, 0, 1, 2)

        container_layout.setAlignment(Qt.AlignmentFlag.AlignLeading)

        layout.addWidget(container)
        return section

    def _create_score_input(self, item: str, max_score: int) -> QWidget:
        """创建单个评分输入组件"""
        item_widget = QWidget()
        item_widget.setFixedHeight(35)  # 只固定高度，不固定宽度
        item_widget.setStyleSheet("""
            QWidget {
                border: none;
                background-color: white;
                padding: 0px;
            }
            QLabel {
                border: none;
            }
        """)

        item_layout = QHBoxLayout(item_widget)
        item_layout.setContentsMargins(10, 0, 10, 0)  # 添加左右内边距
        item_layout.setSpacing(8)  # 统一间距

        # 项目名称
        name_label = QLabel(item)
        name_label.setFixedSize(80, 24)
        name_label.setFont(QFont("Arial", 14))
        name_label.setAlignment(Qt.AlignmentFlag.AlignTrailing)
        item_layout.addWidget(name_label)

        # 分值输入
        score_input = QLineEdit()
        score_input.setFixedSize(60, 24)  # 统一输入框大小
        score_input.setFont(QFont("Arial", 14))
        score_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        score_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                padding: 1px 2px;
                min-height: 24px;
                max-height: 24px;
                margin-left: 10px;
                margin-right: 10px;
            }
            QLineEdit:focus {
                border: 1px solid #409eff;
                background-color: #f0f8ff;
            }
        """)
        score_input.setPlaceholderText(str(max_score))
        item_layout.addWidget(score_input)

        # 参考值
        ref_label = QLabel(f"参考: {max_score}分")
        ref_label.setFixedSize(100, 24)  # 最小宽度而不是固定宽度
        ref_label.setFont(QFont("Arial", 14))
        ref_label.setStyleSheet("color: #666;")
        item_layout.addWidget(ref_label)

        # 添加弹性空间对齐，让组件填充整个列宽
        item_layout.addStretch()

        return item_widget, score_input

    def _create_moca_section(self) -> QWidget:
        """创建MoCA评估区域"""
        section = QFrame()
        section.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        layout = QVBoxLayout(section)
        layout.setSpacing(0)

        # 标题
        title = QLabel("MoCA (蒙特利尔认知评估)")
        title.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        title.setStyleSheet(
            "color: #333; margin-bottom: 15px;border: none;padding: 0px;"
        )
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)

        # 创建网格布局容器
        container = QWidget()
        container.setStyleSheet("background-color: white;")
        container_layout = QGridLayout(container)
        container_layout.setHorizontalSpacing(20)
        container_layout.setVerticalSpacing(10)
        container_layout.setContentsMargins(0, 0, 0, 0)

        # 设置列宽比例，让两列占满宽度
        container_layout.setColumnStretch(0, 1)  # 第一列
        container_layout.setColumnStretch(1, 1)  # 第二列

        # 创建输入字段，使用x*2网格布局
        for i, item_data in enumerate(self.moca_items):
            item_name = item_data["name"]
            item_label = item_data["label"]
            max_score = item_data["max_score"]
            item_widget, score_input = self._create_score_input(item_label, max_score)
            self.moca_inputs[item_name] = score_input

            # 计算网格位置：x行2列
            row = i // 2  # 每行2个项目
            col = i % 2  # 列位置
            container_layout.addWidget(item_widget, row, col)

        container_layout.setAlignment(Qt.AlignmentFlag.AlignLeading)

        layout.addWidget(container)
        return section

    def _create_button_section(self) -> QWidget:
        """创建按钮区域"""
        section = QWidget()
        layout = QHBoxLayout(section)
        layout.setSpacing(20)

        # 返回按钮
        back_btn = QPushButton("返回")
        back_btn.setFixedSize(120, 40)
        back_btn.setFont(QFont("Arial", 14))
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #909399;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #a6a9ad;
            }
            QPushButton:pressed {
                background-color: #82848a;
            }
        """)
        back_btn.clicked.connect(self._on_back_clicked)
        layout.addWidget(back_btn)

        # 提交按钮
        submit_btn = QPushButton("开始实验")
        submit_btn.setFixedSize(120, 40)
        submit_btn.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        submit_btn.setStyleSheet("""
            QPushButton {
                background-color: #409eff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: #66b1ff;
            }
            QPushButton:pressed {
                background-color: #3a8ee6;
            }
        """)
        submit_btn.clicked.connect(self._on_submit_clicked)
        layout.addWidget(submit_btn)

        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        return section

    def _on_back_clicked(self) -> None:
        """返回按钮点击事件 - 委托给Presenter处理"""
        mmse_data = self.get_mmse_data()
        moca_data = self.get_moca_data()
        
        # 委托给Presenter处理
        if self.presenter:
            self.presenter.handle_back_clicked(mmse_data, moca_data)
        else:
            print("⚠️ Presenter未设置，跳过处理")

    def _on_submit_clicked(self) -> None:
        """提交按钮点击事件 - 委托给Presenter处理"""
        # 获取表单数据
        mmse_data = self.get_mmse_data()
        moca_data = self.get_moca_data()

        # 委托给Presenter处理所有验证和后续操作
        if self.presenter:
            self.presenter.handle_form_submit(mmse_data, moca_data)
        else:
            print("⚠️ Presenter未设置，跳过验证")

    def get_mmse_data(self) -> Dict[str, int | None]:
        """获取MMSE评估数据"""
        mmse_data = {}
        for item_data in self.mmse_items:
            item = item_data["name"]
            score_input = self.mmse_inputs.get(item)
            if score_input and score_input.text().strip():
                try:
                    score = int(score_input.text().strip())
                    mmse_data[item] = score
                except ValueError:
                    # 输入无效，不添加到数据中，让验证器处理
                    pass
            else:
                # 如果输入框为空，设置为None
                mmse_data[item] = None
        return mmse_data

    def get_moca_data(self) -> Dict[str, int | None]:
        """获取MoCA评估数据"""
        moca_data = {}
        for item_data in self.moca_items:
            item = item_data["name"]
            score_input = self.moca_inputs.get(item)
            if score_input and score_input.text().strip():
                try:
                    score = int(score_input.text().strip())
                    moca_data[item] = score
                except ValueError:
                    # 输入无效，不添加到数据中，让验证器处理
                    pass
            else:
                # 如果输入框为空，设置为None
                moca_data[item] = None
        return moca_data

    def clear_forms(self) -> None:
        """清空所有表单"""
        try:
            for input_field in self.mmse_inputs.values():
                if input_field is not None and input_field.isWidgetType():
                    input_field.clear()
            for input_field in self.moca_inputs.values():
                if input_field is not None and input_field.isWidgetType():
                    input_field.clear()
        except RuntimeError as e:
            print(f"⚠️  清空表单时遇到对象已删除错误: {e}")

    def restore_forms(self, assessment_data: dict) -> None:
        """恢复表单数据"""
        print("🔄 恢复评估表单数据")
        print(f"📝 恢复评估数据: {assessment_data}")

        try:
            # 恢复MMSE数据
            mmse_data = assessment_data.get("mmse", {})
            for item_name, score in mmse_data.items():
                if item_name in self.mmse_inputs:
                    input_field = self.mmse_inputs[item_name]
                    if input_field and input_field.isWidgetType():
                        # 处理None值，None时清空输入框
                        if score is None:
                            input_field.clear()
                        else:
                            input_field.setText(str(score))

            # 恢复MoCA数据
            moca_data = assessment_data.get("moca", {})
            print(f"📝 MoCA数据: {moca_data}")
            for item_name, score in moca_data.items():
                if item_name in self.moca_inputs:
                    input_field = self.moca_inputs[item_name]
                    if input_field and input_field.isWidgetType():
                        # 处理None值，None时清空输入框
                        if score is None:
                            input_field.clear()
                        else:
                            input_field.setText(str(score))

            print("✅ 评估表单数据恢复完成")

        except Exception as e:
            print(f"❌ 恢复评估表单数据失败: {e}")
            import traceback

            traceback.print_exc()

        print("🔄 评估表单数据恢复结束")

    def set_forms_enabled(self, enabled: bool) -> None:
        """设置表单是否可用"""
        # 设置MMSE输入框
        for input_field in self.mmse_inputs.values():
            if input_field and input_field.isWidgetType():
                input_field.setEnabled(enabled)

        # 设置MoCA输入框
        for input_field in self.moca_inputs.values():
            if input_field and input_field.isWidgetType():
                input_field.setEnabled(enabled)

    def show_error(self, message: str) -> None:
        """显示错误消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("输入错误")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
                color: black;
            }
            QMessageBox QLabel {
                background-color: white;
                color: #333333;
            }
            QMessageBox QPushButton {
                background-color: #409eff;
                color: white;
                border: none;
                padding: 6px 20px;
                border-radius: 4px;
                min-width: 60px;
            }
        """)
        msg_box.exec()

    def _show_error(self, message: str) -> None:
        """显示错误消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("输入错误")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Warning)
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
                color: black;
            }
            QMessageBox QLabel {
                background-color: white;
                color: #333333;
            }
            QMessageBox QPushButton {
                background-color: #409eff;
                color: white;
                border: none;
                padding: 6px 20px;
                border-radius: 4px;
                min-width: 60px;
            }
        """)
        msg_box.exec()
