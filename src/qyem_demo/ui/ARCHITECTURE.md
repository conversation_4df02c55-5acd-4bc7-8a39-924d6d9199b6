# UI架构重构说明 - MVP模式

## 概述

本次重构将原有的MVVM/MVC混合架构统一转换为MVP（Model-View-Presenter）架构模式，实现了视图层与业务逻辑层的完全分离，避免了多重继承问题，确保了功能的正确性。

## MVP架构层次

### 1. Model层 (数据模型层)
- **职责**: 定义数据结构和业务实体
- **组件**:
  - `UserInfo`: 用户信息数据模型
  - `MMSEData`: MMSE评估数据模型
  - `MoCAData`: MoCA评估数据模型
  - `AssessmentData`: 完整评估数据模型
- **特点**:
  - 纯数据对象，不包含UI逻辑
  - 提供数据验证和转换方法
  - 独立于UI框架

### 2. View层 (视图层)
- **职责**: 纯UI展示和用户交互
- **组件**:
  - `HomePage`: 用户信息录入界面
  - `AssessmentPage`: 认知评估界面
  - `PlaybackWindow`: 实验播放窗口
- **特点**:
  - 不包含任何业务逻辑
  - 使用组合模式实现接口，避免多重继承
  - 通过接口与Presenter通信
  - 只负责数据展示和用户输入收集

### 3. Presenter层 (表示层)
- **职责**: 处理业务逻辑、数据验证和协调View与Model
- **组件**:
  - `HomePresenter`: 用户信息业务逻辑
  - `AssessmentPresenter`: 认知评估业务逻辑
  - `ExperimentPresenter`: 实验控制业务逻辑
- **特点**:
  - 包含所有业务逻辑和数据验证
  - 协调View和Model之间的交互
  - 管理应用状态
  - 独立于UI框架，便于测试

### 4. 接口层 (Interface Layer)
- **职责**: 定义各层之间的通信协议
- **组件**:
  - `IHomeView`: HomePage视图接口
  - `IAssessmentView`: AssessmentPage视图接口
  - `IHomePresenter`: 用户信息Presenter接口
  - `IAssessmentPresenter`: 评估Presenter接口
  - `IExperimentPresenter`: 实验Presenter接口
- **特点**:
  - 使用组合模式避免多重继承
  - 定义清晰的契约
  - 支持依赖注入和测试

## MVP目录结构

```
src/qyem_demo/ui/
├── __init__.py
├── main_window.py          # 主窗口，MVP架构协调者
├── pages/                  # View层
│   ├── __init__.py
│   ├── home_page.py        # 用户信息录入视图
│   └── assessment_page.py  # 认知评估视图
├── presenters/             # Presenter层
│   ├── __init__.py
│   ├── home_presenter.py   # 用户信息业务逻辑
│   ├── assessment_presenter.py  # 评估业务逻辑
│   └── experiment_presenter.py  # 实验控制业务逻辑
├── models/                 # Model层
│   ├── __init__.py
│   ├── user_info.py        # 用户信息数据模型
│   └── assessment_data.py  # 评估数据模型
├── interfaces/             # 接口定义
│   ├── __init__.py
│   ├── view_interfaces.py  # View接口
│   └── presenter_interfaces.py  # Presenter接口
└── playback_window.py      # 播放窗口
```

## MVP通信流程

### 用户信息录入流程
1. **HomePage (View)** 收集用户输入
2. **HomePage** 调用 **HomePresenter** 的 `handle_form_submit()` 方法
3. **HomePresenter** 验证数据并与 **UserInfo (Model)** 交互
4. **HomePresenter** 通过 **IHomeView** 接口反馈结果给View
5. **HomePage** 根据结果发送信号给 **MainWindow**

### 认知评估流程
1. **AssessmentPage (View)** 收集评估数据
2. **AssessmentPage** 调用 **AssessmentPresenter** 的 `handle_form_submit()` 方法
3. **AssessmentPresenter** 验证数据并与 **AssessmentData (Model)** 交互
4. **AssessmentPresenter** 通过 **IAssessmentView** 接口反馈结果给View
5. **AssessmentPage** 根据结果发送信号给 **MainWindow**

### 实验控制流程
1. **MainWindow** 创建 **ExperimentPresenter** 并设置回调
2. **PlaybackWindow** 通过 **ExperimentPresenter** 控制实验流程
3. **ExperimentPresenter** 协调数据采集器和范式服务
4. **ExperimentPresenter** 通过回调机制通知UI状态变化

## 优势

1. **单一职责**: 每个层次都有明确的职责边界
2. **可测试性**: ViewModel层可以独立测试
3. **可维护性**: 修改UI不影响业务逻辑，反之亦然
4. **可扩展性**: 新增功能只需在对应层次添加代码
5. **解耦**: 各层之间通过接口通信，降低耦合度

## 使用示例

### 在MainWindow中使用ViewModels

```python
# 验证用户信息
if not self.home_viewmodel.validate_user_info(user_data):
    errors = self.home_viewmodel.get_validation_errors(user_data)
    self.home_page.show_error(error_message)
    return

# 保存用户信息
self.home_viewmodel.save_user_info(user_data)
```

### 在ViewModels中使用数据模型

```python
# 创建数据对象
user_info = UserInfo.from_dict(user_data)
if user_info.is_valid():
    self._user_info = user_info
```

## 未来扩展

该架构支持以下扩展：
- 添加新的页面和对应的ViewModel
- 替换UI框架（如从PySide6切换到其他框架）
- 添加数据持久化层
- 实现单元测试