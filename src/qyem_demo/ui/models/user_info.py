"""
用户信息数据模型
"""

from typing import Optional
from dataclasses import dataclass
from datetime import date


@dataclass
class UserInfo:
    """用户信息数据模型"""
    
    name: str = ""
    birth_date: Optional[date] = None
    education: str = ""
    gender: Optional[bool] = None  # True for male, False for female
    
    @classmethod
    def from_dict(cls, data: dict) -> "UserInfo":
        """从字典创建UserInfo对象"""
        from PySide6.QtCore import QDate
        
        user_info = cls()
        user_info.name = data.get("name", "")
        
        # 解析出生日期
        birth_date_str = data.get("birth_date", "")
        if birth_date_str:
            try:
                qdate = QDate.fromString(birth_date_str, "yyyy-MM-dd")
                if qdate.isValid():
                    user_info.birth_date = qdate.toPython()
            except Exception:
                pass
        
        user_info.education = data.get("education", "")
        user_info.gender = data.get("gender")
        
        return user_info
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "name": self.name,
            "birth_date": self.birth_date.isoformat() if self.birth_date else "",
            "education": self.education,
            "gender": self.gender,
        }
    
    def is_valid(self) -> bool:
        """验证用户信息是否完整有效"""
        return bool(
            self.name.strip() and
            self.birth_date is not None and
            self.education and self.education != "请选择教育程度" and
            self.gender is not None
        )