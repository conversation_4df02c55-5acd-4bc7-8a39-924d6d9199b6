"""
认知评估数据模型
"""

from typing import Dict
from dataclasses import dataclass


@dataclass
class MMSEData:
    """MMSE评估数据模型"""
    
    orientation: int | None = None          # 定向力 (0-10)
    instant_memory: int | None = None       # 瞬时记忆 (0-3)
    calculation: int | None = None          # 计算力 (0-5)
    short_term_memory: int | None = None    # 短时记忆 (0-3)
    naming: int | None = None               # 命名 (0-2)
    repetition: int | None = None           # 复述 (0-1)
    reading: int | None = None              # 阅读 (0-1)
    execution: int | None = None            # 执行 (0-3)
    writing: int | None = None              # 书写 (0-1)
    structure_imitation: int | None = None  # 结构模仿 (0-1)
    
    @property
    def total_score(self) -> int:
        """计算总分"""
        return (
            (self.orientation or 0) + (self.instant_memory or 0) + (self.calculation or 0) +
            (self.short_term_memory or 0) + (self.naming or 0) + (self.repetition or 0) +
            (self.reading or 0) + (self.execution or 0) + (self.writing or 0) + (self.structure_imitation or 0)
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, int]) -> "MMSEData":
        """从字典创建MMSEData对象"""
        mmse = cls()
        mmse.orientation = data.get("orientation")
        mmse.instant_memory = data.get("instant_memory")
        mmse.calculation = data.get("calculation")
        mmse.short_term_memory = data.get("short_term_memory")
        mmse.naming = data.get("naming")
        mmse.repetition = data.get("repetition")
        mmse.reading = data.get("reading")
        mmse.execution = data.get("execution")
        mmse.writing = data.get("writing")
        mmse.structure_imitation = data.get("structure_imitation")
        return mmse
    
    def to_dict(self) -> Dict[str, int | None]:
        """转换为字典格式"""
        return {
            "orientation": self.orientation,
            "instant_memory": self.instant_memory,
            "calculation": self.calculation,
            "short_term_memory": self.short_term_memory,
            "naming": self.naming,
            "repetition": self.repetition,
            "reading": self.reading,
            "execution": self.execution,
            "writing": self.writing,
            "structure_imitation": self.structure_imitation,
        }


@dataclass
class MoCAData:
    """MoCA评估数据模型"""
    
    visuospatial_execution: int | None = None  # 视空间/执行 (0-2)
    clock_drawing: int | None = None           # 画钟 (0-3)
    naming: int | None = None                  # 命名 (0-3)
    attention: int | None = None               # 注意力 (0-3)
    calculation: int | None = None             # 计算力 (0-3)
    language: int | None = None                # 语言 (0-3)
    abstraction: int | None = None             # 抽象能力 (0-2)
    delayed_recall: int | None = None          # 延迟记忆 (0-5)
    orientation: int | None = None             # 定向力 (0-6)
    
    @property
    def total_score(self) -> int:
        """计算总分"""
        return (
            (self.visuospatial_execution or 0) + (self.clock_drawing or 0) + (self.naming or 0) +
            (self.attention or 0) + (self.calculation or 0) + (self.language or 0) +
            (self.abstraction or 0) + (self.delayed_recall or 0) + (self.orientation or 0)
        )
    
    @classmethod
    def from_dict(cls, data: Dict[str, int]) -> "MoCAData":
        """从字典创建MoCAData对象"""
        moca = cls()
        moca.visuospatial_execution = data.get("visuospatial_execution")
        moca.clock_drawing = data.get("clock_drawing")
        moca.naming = data.get("naming")
        moca.attention = data.get("attention")
        moca.calculation = data.get("calculation")
        moca.language = data.get("language")
        moca.abstraction = data.get("abstraction")
        moca.delayed_recall = data.get("delayed_recall")
        moca.orientation = data.get("orientation")
        return moca
    
    def to_dict(self) -> Dict[str, int | None]:
        """转换为字典格式"""
        return {
            "visuospatial_execution": self.visuospatial_execution,
            "clock_drawing": self.clock_drawing,
            "naming": self.naming,
            "attention": self.attention,
            "calculation": self.calculation,
            "language": self.language,
            "abstraction": self.abstraction,
            "delayed_recall": self.delayed_recall,
            "orientation": self.orientation,
        }


@dataclass
class AssessmentData:
    """完整的评估数据模型"""
    
    mmse: MMSEData
    moca: MoCAData
    
    def __post_init__(self):
        """初始化后的处理"""
        if isinstance(self.mmse, dict):
            self.mmse = MMSEData.from_dict(self.mmse)
        if isinstance(self.moca, dict):
            self.moca = MoCAData.from_dict(self.moca)
    
    @property
    def mmse_total_score(self) -> int:
        """MMSE总分"""
        return self.mmse.total_score
    
    @property
    def moca_total_score(self) -> int:
        """MoCA总分"""
        return self.moca.total_score
    
    @classmethod
    def from_dict(cls, data: Dict) -> "AssessmentData":
        """从字典创建AssessmentData对象"""
        mmse_data = MMSEData.from_dict(data.get("mmse", {}))
        moca_data = MoCAData.from_dict(data.get("moca", {}))
        return cls(mmse=mmse_data, moca=moca_data)
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            "mmse": self.mmse.to_dict(),
            "moca": self.moca.to_dict(),
            "mmse_total": self.mmse_total_score,
            "moca_total": self.moca_total_score,
        }
    
    def is_valid(self) -> bool:
        """验证评估数据是否完整有效"""
        return self.mmse.total_score >= 0 and self.moca.total_score >= 0