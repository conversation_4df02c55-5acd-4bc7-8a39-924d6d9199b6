"""
简化的播放窗口 - 使用新的架构设计
"""

from typing import TYPE_CHECKING

from PySide6.QtCore import QTimer, Qt, Signal
from PySide6.QtGui import QCloseEvent, QFont, QKeyEvent, QPixmap, QResizeEvent
from PySide6.QtWidgets import QLabel, QMainWindow

if TYPE_CHECKING:
    from .presenters.experiment_presenter import ExperimentPresenter

BACKGROUND_COLOR = "#f5f5f5"
TEXT_COLOR = "#333333"
SHOW_LABEL = False


class PlaybackWindow(QMainWindow):
    """
    简化的播放窗口
    
    职责：
    1. 纯UI显示
    2. 用户交互处理
    3. 通过信号与控制器通信
    """

    # 信号定义
    playback_finished = Signal(dict)  # 播放完成
    playback_stopped = Signal(dict)   # 播放中断
    user_exit_requested = Signal()    # 用户请求退出

    def __init__(self, experiment_presenter: "ExperimentPresenter") -> None:
        """
        初始化播放窗口

        Args:
            experiment_presenter: 实验Presenter
        """
        super().__init__()

        self.experiment_presenter = experiment_presenter
        
        # UI元素
        self.image_label: QLabel | None = None
        self.progress_label: QLabel | None = None
        self.exit_hint_label: QLabel | None = None
        
        # 播放状态
        self.is_playing = False
        self.slideshow_timer: QTimer | None = None
        self.current_stimulus_index = 0
        self.total_stimuli = 0
        
        # 初始化UI
        self._init_ui()
        self._connect_signals()

    def _init_ui(self) -> None:
        """初始化用户界面"""
        self.setWindowTitle("眼动数据采集 - 播放窗口")
        self.setStyleSheet(f"background-color: {BACKGROUND_COLOR};")

        # 设置窗口属性但不显示
        self.setWindowFlags(Qt.WindowType.Window | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, False)

        # 主图片显示区域
        self.image_label = QLabel(self)
        self.image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.image_label.setStyleSheet(f"background-color: {BACKGROUND_COLOR};")
        self.image_label.setText("准备中...")  # 初始文本
        self.image_label.setScaledContents(False)  # 不缩放图片，保持原始尺寸

        # 进度显示（右下角）
        self.progress_label = QLabel("0 / 0", self)
        self.progress_label.setFont(QFont("Arial", 14))
        self.progress_label.setStyleSheet(f"color: {TEXT_COLOR}; background: transparent;")
        self.progress_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        if not SHOW_LABEL:
            self.progress_label.hide()

        # ESC键退出提示（左上角）
        self.exit_hint_label = QLabel("按ESC键返回主界面", self)
        self.exit_hint_label.setFont(QFont("Arial", 12))
        self.exit_hint_label.setStyleSheet(f"color: {TEXT_COLOR}; background: transparent;")
        if not SHOW_LABEL:
            self.exit_hint_label.hide()

        # 设置焦点
        self.setFocusPolicy(Qt.FocusPolicy.StrongFocus)

        # 初始化定时器
        self.slideshow_timer = QTimer()
        self.slideshow_timer.setSingleShot(True)
        self.slideshow_timer.timeout.connect(self._next_stimulus)

    def _connect_signals(self) -> None:
        """连接回调"""
        # 设置实验Presenter的回调
        self.experiment_presenter.set_callbacks(
            experiment_started=self._on_experiment_started,
            experiment_finished=self._on_experiment_finished,
            experiment_stopped=self._on_experiment_stopped,
            stimulus_changed=self._on_stimulus_changed,
            error_occurred=self._on_error_occurred
        )

    def start_experiment(self, session_name: str, user_info: dict, assessment_data: dict) -> bool:
        """
        启动实验 - 整合实验Presenter和播放逻辑
        
        Args:
            session_name: 会话名称
            user_info: 用户信息
            assessment_data: 评估数据
            
        Returns:
            是否成功开始
        """
        print(f"🎯 启动完整实验流程: {session_name}")
        
        # 设置会话数据到实验Presenter
        self.experiment_presenter.set_session_data(user_info, assessment_data)
        
        # 隐藏主窗口（由调用者处理）
        
        # 开始播放
        return self.start_playback(session_name, user_info)

    def start_playback(self, session_name: str, user_info: dict) -> bool:
        """
        开始播放

        Args:
            session_name: 会话名称
            user_info: 用户信息

        Returns:
            是否成功开始
        """
        print(f"🎬 开始播放: {session_name}")

        # 确保窗口全屏显示
        self.showFullScreen()
        self.raise_()
        self.activateWindow()

        # 通过Presenter开始实验
        if self.experiment_presenter.start_experiment(session_name, user_info):
            self.is_playing = True
            return True
        else:
            return False

    def stop_playback(self, reason: str = "用户停止") -> None:
        """
        停止播放
        
        Args:
            reason: 停止原因
        """
        if not self.is_playing:
            return
            
        print(f"⏹️ 停止播放: {reason}")
        self.is_playing = False
        
        if self.slideshow_timer:
            self.slideshow_timer.stop()
        
        # 集成实验结束处理流程
        self._handle_experiment_completion(reason)

    def _on_experiment_started(self, session_info: dict) -> None:
        """实验开始回调"""
        self.total_stimuli = session_info.get("stimulus_count", 0)
        self.current_stimulus_index = 0
        self._update_progress()
        print(f"✅ 实验开始，共 {self.total_stimuli} 个刺激")

        # 清除初始文本
        if self.image_label:
            self.image_label.setText("加载中...")

        # 开始播放第一个刺激
        if self.total_stimuli > 0:
            print("🎬 开始播放第一个刺激")
            self.experiment_presenter.set_current_stimulus(0)
        else:
            print("❌ 没有刺激可播放")
            if self.image_label:
                self.image_label.setText("没有刺激可播放")

    def _on_experiment_finished(self, result: dict) -> None:
        """实验完成回调"""
        print("✅ 实验正常完成")
        # 集成实验结束处理流程
        self._handle_experiment_completion("正常完成")

    def _on_experiment_stopped(self, result: dict) -> None:
        """实验中断回调"""
        print(f"⏹️ 实验中断: {result.get('reason', '未知原因')}")
        self.playback_stopped.emit(result)

    def _on_stimulus_changed(self, stimulus_info: dict) -> None:
        """刺激变化回调"""
        self.current_stimulus_index = stimulus_info["index"]
        self._show_stimulus(stimulus_info)
        self._update_progress()
        
        # 设置定时器切换到下一个刺激
        duration_ms = int(stimulus_info["duration"] * 1000)
        if self.slideshow_timer:
            self.slideshow_timer.start(duration_ms)

    def _on_error_occurred(self, error_message: str) -> None:
        """错误发生回调"""
        print(f"❌ 错误: {error_message}")
        # 可以在这里显示错误对话框

    def _show_stimulus(self, stimulus_info: dict) -> None:
        """显示刺激"""
        image_path = stimulus_info["path"]
        collect_status = "采集中" if stimulus_info["collect"] else "不采集"

        print(f"🖼️ 显示刺激 {stimulus_info['index'] + 1}/{stimulus_info['total']}: "
              f"{stimulus_info['name']} ({collect_status})")
        print(f"   图片路径: {image_path}")

        # 检查文件是否存在
        from pathlib import Path
        if not Path(image_path).exists():
            print(f"❌ 图片文件不存在: {image_path}")
            self._next_stimulus()
            return

        # 加载并显示图片
        pixmap = QPixmap(image_path)
        if pixmap.isNull():
            print(f"❌ 无法加载图片: {image_path}")
            self._next_stimulus()
            return

        print(f"✅ 图片加载成功，尺寸: {pixmap.width()}x{pixmap.height()}")

        if self.image_label:
            # 清除之前的文本
            self.image_label.setText("")

            # 设置图片，保持原始尺寸
            self.image_label.setPixmap(pixmap)

            # 居中显示图片
            self._center_image()

            # 强制刷新界面
            self.image_label.update()
            self.update()
            self.repaint()  # 强制重绘

            # 调试信息
            print("✅ 图片已设置到界面（原始尺寸，居中显示）")
            print(f"   图片原始尺寸: {pixmap.width()}x{pixmap.height()}")
            print(f"   窗口尺寸: {self.width()}x{self.height()}")
            print(f"   图片位置: ({self.image_label.x()}, {self.image_label.y()})")
            print(f"   窗口可见: {self.isVisible()}")
        else:
            print("❌ image_label 为空")

    def _next_stimulus(self) -> None:
        """切换到下一个刺激"""
        if not self.is_playing:
            return
            
        next_index = self.current_stimulus_index + 1
        
        # 检查是否还有更多刺激
        if next_index >= self.total_stimuli:
            # 所有刺激播放完成
            self.stop_playback(reason="正常完成")
        else:
            # 设置下一个刺激
            self.experiment_presenter.set_current_stimulus(next_index)

    def _update_progress(self) -> None:
        """更新进度显示"""
        if self.progress_label and SHOW_LABEL:
            progress_text = f"{self.current_stimulus_index + 1} / {self.total_stimuli}"
            self.progress_label.setText(progress_text)

    def keyPressEvent(self, event: QKeyEvent) -> None:
        """处理按键事件"""
        if event.key() == Qt.Key.Key_Escape:
            print("⌨️ 用户按下ESC键")
            if not self.is_playing:
                return

            self.stop_playback(reason="用户中断")
        super().keyPressEvent(event)

    def resizeEvent(self, event: QResizeEvent) -> None:
        """窗口大小改变事件"""
        super().resizeEvent(event)

        # 重新居中图片
        self._center_image()

        # 调整其他UI元素位置
        if self.progress_label and SHOW_LABEL:
            self.progress_label.resize(200, 30)
            self.progress_label.move(self.width() - 210, self.height() - 40)

        if self.exit_hint_label and SHOW_LABEL:
            self.exit_hint_label.resize(200, 30)
            self.exit_hint_label.move(10, 10)

    def _center_image(self) -> None:
        """居中显示当前图片"""
        if not self.image_label:
            return

        # 检查是否有图片
        current_pixmap = self.image_label.pixmap()
        if not current_pixmap or current_pixmap.isNull():
            return

        # 调整image_label尺寸以适应图片
        self.image_label.resize(current_pixmap.size())

        # 计算居中位置
        window_width = self.width()
        window_height = self.height()
        image_width = current_pixmap.width()
        image_height = current_pixmap.height()

        x = (window_width - image_width) // 2
        y = (window_height - image_height) // 2

        # 设置居中位置
        self.image_label.move(x, y)

    def _handle_experiment_completion(self, reason: str) -> None:
        """
        集成实验结束处理流程
        处理所有实验结束场景：正常完成、用户中断、数据质量问题等
        """
        try:
            # 1. 获取采集数据
            data = self.experiment_presenter.collector.get_collected_data()
            data_count = len(data) if data else 0
            
            # 2. 检查数据质量（如果有数据）
            quality_result = None
            if data_count > 0:
                quality_result = self.experiment_presenter.collector.check_data_quality()
                is_data_valid = quality_result["valid"]
            else:
                is_data_valid = False
            
            # 3. 根据情况决定处理流程
            if data_count == 0 or not is_data_valid:
                # 场景：未采集到数据 或 数据质量不合格
                from qyem_demo.quality_checker import show_data_collection_dialog
                user_wants_retry = show_data_collection_dialog(data_count, quality_result)
                
                if user_wants_retry:
                    # 用户选择重新采集
                    print("🔄 用户选择重新采集")
                    result = {
                        "success": False,
                        "reason": f"{reason}，用户选择重新采集",
                        "retry_requested": True,
                        "data_count": data_count,
                        "quality_result": quality_result,
                        "save_data": False
                    }
                    self.playback_stopped.emit(result)
                else:
                    # 用户选择不重新采集，跳过保存
                    print("⏭️ 用户选择不重新采集")
                    result = {
                        "success": False,
                        "reason": f"{reason}，用户选择不重新采集",
                        "retry_requested": False,
                        "data_count": data_count,
                        "quality_result": quality_result,
                        "save_data": False
                    }
                    self.playback_finished.emit(result)
            else:
                # 场景：数据质量合格，正常保存
                print("💾 数据质量合格，保存数据")
                result = {
                    "success": True,
                    "reason": reason,
                    "retry_requested": False,
                    "data_count": data_count,
                    "quality_result": quality_result,
                    "save_data": True
                }
                self.playback_finished.emit(result)
                
        except Exception as e:
            print(f"⚠️ 实验结束处理失败: {e}")
            result = {
                "success": False,
                "reason": f"{reason}，处理失败: {e}",
                "retry_requested": False,
                "data_count": 0,
                "quality_result": None,
                "save_data": False
            }
            self.playback_finished.emit(result)
        finally:
            # 始终隐藏播放窗口
            self.hide()

    def closeEvent(self, event: QCloseEvent) -> None:
        """窗口关闭事件"""
        if self.is_playing:
            self.stop_playback(reason="窗口关闭")
        event.accept()
