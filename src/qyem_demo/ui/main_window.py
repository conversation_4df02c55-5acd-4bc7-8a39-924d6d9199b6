"""
主窗口模块 - MVP架构的View层
"""

from PySide6.QtGui import QCloseEvent
from PySide6.QtWidgets import QMainWindow, QMessageBox

from .pages import HomePage, AssessmentPage
from .playback_window import PlaybackWindow


class MainWindow(QMainWindow):
    """主窗口类 - MVP架构的View层"""

    def __init__(self) -> None:
        """初始化主窗口 - 纯粹的View组件"""
        super().__init__()

        # 设置窗口属性
        self.setWindowTitle("眼动仪数据采集系统")
        self.setMinimumSize(800, 600)  # 最小尺寸
        self.resize(1200, 900)  # 默认尺寸
        self.setStyleSheet("background-color: #fafafa;")

        # 页面管理
        self.home_page: HomePage | None = None
        self.assessment_page: AssessmentPage | None = None
        
        # 外部设置属性
        self.playback_window: PlaybackWindow | None = None

        # 初始化UI
        self._init_ui()

    def _init_ui(self) -> None:
        """初始化用户界面"""
        # 页面将在设置presenter后创建
        pass

    def set_presenters(self, home_presenter, assessment_presenter) -> None:
        """设置Presenter并创建页面"""
        self.home_page = HomePage(presenter=home_presenter)
        self.assessment_page = AssessmentPage(presenter=assessment_presenter)
        
        # 设置对象名称以便调试
        self.home_page.setObjectName("HomePage")
        self.assessment_page.setObjectName("AssessmentPage")

    # IMainWindowView接口实现
    def show_home_page(self) -> None:
        """显示用户信息页面"""
        if self._is_home_page_valid():
            self.setCentralWidget(self.home_page)
        else:
            print("❌ HomePage无效，重新创建")
            self.home_page = HomePage(presenter=self.home_presenter)
            self.home_page.setObjectName("HomePage")
            self.setCentralWidget(self.home_page)
        
        # 确保主窗口可见并激活
        self.show()
        self.raise_()
        self.activateWindow()
        print("🏠 主窗口已恢复显示")

    def show_assessment_page(self) -> None:
        """显示评估页面"""
        if self._is_assessment_page_valid():
            self.setCentralWidget(self.assessment_page)
        else:
            print("❌ AssessmentPage无效，重新创建")
            self.assessment_page = AssessmentPage(presenter=self.assessment_presenter)
            self.assessment_page.setObjectName("AssessmentPage")
            self.setCentralWidget(self.assessment_page)

    def restore_user_info(self, user_info: dict) -> None:
        """恢复用户信息表单"""
        if self._is_home_page_valid():
            self.home_page.restore_form(user_info)

    def restore_assessment_data(self, assessment_data: dict) -> None:
        """恢复评估数据表单"""
        if self._is_assessment_page_valid():
            self.assessment_page.restore_forms(assessment_data)


    def show_error(self, message: str) -> None:
        """显示错误消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("错误")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setStyleSheet(
            """
            QMessageBox { background-color: white; color: black; }
            QMessageBox QLabel { background-color: white; color: #333; }
            QMessageBox QPushButton { background-color: #f56c6c; color: white; border: none; padding: 6px 20px; border-radius: 4px; }
            QMessageBox QPushButton:hover { background-color: #f78989; }
            QMessageBox QPushButton:pressed { background-color: #dd6161; }
        """
        )
        msg_box.exec()

    def show_success(self, message: str) -> None:
        """显示成功消息"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("成功")
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setStyleSheet(
            """
            QMessageBox { background-color: white; color: black; }
            QMessageBox QLabel { background-color: white; color: #333; }
            QMessageBox QPushButton { background-color: #409eff; color: white; border: none; padding: 6px 20px; border-radius: 4px; }
            QMessageBox QPushButton:hover { background-color: #66b1ff; }
            QMessageBox QPushButton:pressed { background-color: #3a8ee6; }
        """
        )
        msg_box.exec()



    def set_experiment_callbacks(self, on_complete_callback) -> None:
        """设置实验完成回调"""
        if self.playback_window:
            self.playback_window.playback_finished.connect(on_complete_callback)
            self.playback_window.playback_stopped.connect(on_complete_callback)

    def _restore_main_window(self) -> None:
        """恢复主窗口显示"""
        self.show()
        self.raise_()
        self.activateWindow()
        print("🏠 主窗口已恢复显示")

    def _is_home_page_valid(self) -> bool:
        """检查HomePage对象是否有效"""
        if not self.home_page:
            return False
        try:
            _ = self.home_page.objectName()
            return True
        except RuntimeError as e:
            print(f"⚠️ HomePage对象已被删除: {e}")
            self.home_page = None
            return False

    def _is_assessment_page_valid(self) -> bool:
        """检查AssessmentPage对象是否有效"""
        if not self.assessment_page:
            return False
        try:
            _ = self.assessment_page.objectName()
            return True
        except RuntimeError as e:
            print(f"⚠️ AssessmentPage对象已被删除: {e}")
            self.assessment_page = None
            return False

    def closeEvent(self, event: QCloseEvent) -> None:
        """窗口关闭事件"""
        print("👋 正在关闭应用程序...")

        # 关闭播放窗口
        if self.playback_window:
            self.playback_window.close()

        if self.et_session:
            print("🔌 断开眼动仪连接")

        event.accept()