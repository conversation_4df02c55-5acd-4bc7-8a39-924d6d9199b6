"""
MVP架构 - Presenter接口定义
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .view_interfaces import IHomeView, IAssessmentView


class IHomePresenter(ABC):
    """用户信息Presenter接口"""

    @abstractmethod
    def set_view(self, view: "IHomeView") -> None:
        """设置关联的View"""
        pass

    @abstractmethod
    def validate_user_info(self, user_data: Dict[str, Any]) -> bool:
        """验证用户信息"""
        pass

    @abstractmethod
    def save_user_info(self, user_data: Dict[str, Any]) -> bool:
        """保存用户信息"""
        pass

    @abstractmethod
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取已保存的用户信息"""
        pass

    @abstractmethod
    def clear_user_info(self) -> None:
        """清除用户信息"""
        pass

    @abstractmethod
    def get_validation_errors(self, user_data: Dict[str, Any]) -> Dict[str, str]:
        """获取验证错误详情"""
        pass

    @abstractmethod
    def handle_form_submit(self, user_data: Dict[str, Any]) -> None:
        """处理表单提交"""
        pass
    
    @abstractmethod
    def set_on_complete_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """设置完成回调"""
        pass


class IAssessmentPresenter(ABC):
    """认知评估Presenter接口"""

    @abstractmethod
    def set_view(self, view: "IAssessmentView") -> None:
        """设置关联的View"""
        pass

    @abstractmethod
    def validate_assessment_data(
        self, mmse_data: Dict[str, int | None], moca_data: Dict[str, int | None]
    ) -> bool:
        """验证评估数据"""
        pass

    @abstractmethod
    def save_assessment_data(
        self, mmse_data: Dict[str, int | None], moca_data: Dict[str, int | None]
    ) -> None:
        """保存评估数据"""
        pass

    @abstractmethod
    def get_assessment_data(self) -> Optional[Dict[str, Any]]:
        """获取已保存的评估数据"""
        pass

    @abstractmethod
    def clear_assessment_data(self) -> None:
        """清除评估数据"""
        pass

    @abstractmethod
    def get_validation_errors(
        self,
        mmse_data: Dict[str, int | None],
        moca_data: Dict[str, int | None],
        only_one_error: bool = False,
    ) -> Dict[str, str]:
        """获取验证错误详情"""
        pass

    @abstractmethod
    def handle_form_submit(
        self, mmse_data: Dict[str, int | None], moca_data: Dict[str, int | None]
    ) -> None:
        """处理表单提交"""
        pass
    
    @abstractmethod
    def handle_back_clicked(
        self, mmse_data: Dict[str, int | None], moca_data: Dict[str, int | None]
    ) -> None:
        """处理返回按钮点击"""
        pass
    
    @abstractmethod
    def set_on_complete_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """设置完成回调"""
        pass
    
    @abstractmethod
    def set_on_back_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """设置返回回调"""
        pass


class IExperimentPresenter(ABC):
    """实验控制Presenter接口"""

    @abstractmethod
    def start_experiment(self, session_name: str, user_info: Dict[str, Any]) -> bool:
        """开始实验"""
        pass

    @abstractmethod
    def stop_experiment(
        self, save_data: bool = True, reason: str = "用户停止"
    ) -> Dict[str, Any]:
        """停止实验"""
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查实验是否正在运行"""
        pass

    @abstractmethod
    def get_experiment_status(self) -> Dict[str, Any]:
        """获取实验状态"""
        pass

    @abstractmethod
    def set_callbacks(
        self,
        experiment_started: Optional[Callable[[Dict[str, Any]], None]] = None,
        experiment_finished: Optional[Callable[[Dict[str, Any]], None]] = None,
        experiment_stopped: Optional[Callable[[Dict[str, Any]], None]] = None,
        stimulus_changed: Optional[Callable[[Dict[str, Any]], None]] = None,
        error_occurred: Optional[Callable[[str], None]] = None,
        status_updated: Optional[Callable[[Dict[str, Any]], None]] = None,
    ) -> None:
        """设置回调函数"""
        pass
