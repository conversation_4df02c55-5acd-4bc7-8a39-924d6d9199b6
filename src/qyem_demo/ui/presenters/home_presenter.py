"""
MVP架构 - 用户信息Presenter
"""

from typing import Any, Dict, Optional, TYPE_CHECKING, Callable
from ..interfaces.presenter_interfaces import IHomePresenter
from ..models.user_info import UserInfo

if TYPE_CHECKING:
    from ..interfaces.view_interfaces import IHomeView


class HomePresenter(IHomePresenter):
    """用户信息Presenter - 处理用户信息相关业务逻辑"""
    
    def __init__(self) -> None:
        """初始化Presenter"""
        self._view: Optional["IHomeView"] = None
        self._user_info: Optional[UserInfo] = None
        self._on_complete_callback: Optional[Callable[[Dict[str, Any]], None]] = None
    
    def set_view(self, view: "IHomeView") -> None:
        """设置关联的View"""
        self._view = view
    
    def set_on_complete_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """设置完成回调"""
        self._on_complete_callback = callback
    
    def validate_user_info(self, user_data: Dict[str, Any]) -> bool:
        """
        验证用户信息
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            验证是否通过
        """
        errors = self.get_validation_errors(user_data)
        return len(errors) == 0

    def get_validation_errors(self, user_data: Dict[str, Any]) -> Dict[str, str]:
        """
        获取验证错误的详细信息
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            字段名到错误信息的映射
        """
        errors = {}
        
        # 验证姓名
        name = user_data.get("name", "").strip()
        if not name:
            errors["name"] = "请输入有效的姓名"
        
        # 验证教育程度
        education = user_data.get("education", "")
        if not education or education == "请选择教育程度":
            errors["education"] = "请选择教育程度"
        
        # 验证性别
        gender = user_data.get("gender")
        if gender is None:
            errors["gender"] = "请选择性别"
        
        # 验证出生日期
        birth_date = user_data.get("birth_date", "")
        if not birth_date:
            errors["birth_date"] = "请选择出生日期"
        
        return errors
    
    def save_user_info(self, user_data: Dict[str, Any]) -> bool:
        """
        保存用户信息
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            是否保存成功
        """
        try:
            # 验证数据
            errors = self.get_validation_errors(user_data)
            if errors:
                error_message = "\n".join(errors.values())
                if self._view:
                    self._view.show_error(error_message)
                return False
            
            # 保存数据
            self._user_info = UserInfo.from_dict(user_data)
            return True
            
        except Exception as e:
            if self._view:
                self._view.show_error(f"保存失败: {e}")
            return False
    
    def handle_form_submit(self, user_data: Dict[str, Any]) -> None:
        """
        处理表单提交 - 单一入口点
        
        Args:
            user_data: 用户信息字典
        """
        if self.save_user_info(user_data):
            # 通过回调通知完成，不直接操作View
            if self._on_complete_callback:
                self._on_complete_callback(user_data)
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        获取已保存的用户信息
        
        Returns:
            用户信息字典，如果没有保存则返回None
        """
        if self._user_info is None:
            return None
        return self._user_info.to_dict()
    
    def clear_user_info(self) -> None:
        """清除用户信息"""
        self._user_info = None
