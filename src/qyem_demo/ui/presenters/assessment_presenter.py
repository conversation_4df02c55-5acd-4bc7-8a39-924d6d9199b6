"""
MVP架构 - 认知评估Presenter
"""

from typing import Any, Dict, Optional, TYPE_CHECKING, Callable
from ..interfaces.presenter_interfaces import IAssessmentPresenter
from ..models.assessment_data import AssessmentData, MMSEData, MoCAData

if TYPE_CHECKING:
    from ..interfaces.view_interfaces import IAssessmentView


class AssessmentPresenter(IAssessmentPresenter):
    """认知评估Presenter - 处理认知评估相关业务逻辑"""

    def __init__(self) -> None:
        """初始化Presenter"""
        self._view: Optional["IAssessmentView"] = None
        self._assessment_data: Optional[AssessmentData] = None
        self._on_complete_callback: Optional[Callable[[Dict[str, Any]], None]] = None
        self._on_back_callback: Optional[Callable[[Dict[str, Any]], None]] = None

        # MMSE评估项目配置
        self.mmse_items = [
            {"name": "orientation", "label": "定向力", "max_score": 10},
            {"name": "instant_memory", "label": "瞬时记忆", "max_score": 3},
            {"name": "calculation", "label": "计算力", "max_score": 5},
            {"name": "short_term_memory", "label": "短时记忆", "max_score": 3},
            {"name": "naming", "label": "命名", "max_score": 2},
            {"name": "repetition", "label": "复述", "max_score": 1},
            {"name": "reading", "label": "阅读", "max_score": 1},
            {"name": "execution", "label": "执行", "max_score": 3},
            {"name": "writing", "label": "书写", "max_score": 1},
            {"name": "structure_imitation", "label": "结构模仿", "max_score": 1},
        ]

        # MoCA评估项目配置
        self.moca_items = [
            {"name": "visuospatial_execution", "label": "视空间/执行", "max_score": 2},
            {"name": "clock_drawing", "label": "画钟", "max_score": 3},
            {"name": "naming", "label": "命名", "max_score": 3},
            {"name": "attention", "label": "注意力", "max_score": 3},
            {"name": "calculation", "label": "计算力", "max_score": 3},
            {"name": "language", "label": "语言", "max_score": 3},
            {"name": "abstraction", "label": "抽象能力", "max_score": 2},
            {"name": "delayed_recall", "label": "延迟记忆", "max_score": 5},
            {"name": "orientation", "label": "定向力", "max_score": 6},
        ]

    def set_view(self, view: "IAssessmentView") -> None:
        """设置关联的View"""
        self._view = view

    def set_on_complete_callback(
        self, callback: Callable[[Dict[str, Any]], None]
    ) -> None:
        """设置完成回调"""
        self._on_complete_callback = callback

    def set_on_back_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """设置返回回调"""
        self._on_back_callback = callback

    def validate_assessment_data(
        self, mmse_data: Dict[str, int | None], moca_data: Dict[str, int | None]
    ) -> bool:
        """
        验证评估数据

        Args:
            mmse_data: MMSE评估数据
            moca_data: MoCA评估数据

        Returns:
            验证是否通过
        """
        try:
            # 验证MMSE数据
            for item_config in self.mmse_items:
                item_name = item_config["name"]
                max_score = item_config["max_score"]
                score = mmse_data.get(item_name)
                # TODO: 暂时不验证
                # if score is None:
                #     return False
                # if not isinstance(score, int):
                #     return False
                # if not (0 <= score <= max_score):
                #     return False

            # 验证MoCA数据
            for item_config in self.moca_items:
                item_name = item_config["name"]
                max_score = item_config["max_score"]
                score = moca_data.get(item_name)
                # TODO: 暂时不验证
                # if score is None:
                #     return False
                # if not isinstance(score, int):
                #     return False
                # if not (0 <= score <= max_score):
                #     return False

            # 尝试创建数据模型进行进一步验证
            mmse = MMSEData.from_dict(mmse_data)
            moca = MoCAData.from_dict(moca_data)
            assessment = AssessmentData(mmse=mmse, moca=moca)

            # 验证数据模型是否有效
            if not assessment.is_valid():
                return False

            return True
        except Exception:
            return False

    def save_assessment_data(
        self, mmse_data: Dict[str, int | None], moca_data: Dict[str, int | None]
    ) -> None:
        """
        保存评估数据

        Args:
            mmse_data: MMSE评估数据
            moca_data: MoCA评估数据
        """
        try:
            mmse = MMSEData.from_dict(mmse_data)
            moca = MoCAData.from_dict(moca_data)
            self._assessment_data = AssessmentData(mmse=mmse, moca=moca)
        except Exception as e:
            raise ValueError(f"保存评估数据失败: {e}")

    def get_assessment_data(self) -> Optional[Dict[str, Any]]:
        """
        获取已保存的评估数据

        Returns:
            评估数据字典，如果没有保存则返回None
        """
        if self._assessment_data is None:
            return None
        return self._assessment_data.to_dict()

    def clear_assessment_data(self) -> None:
        """清除评估数据"""
        self._assessment_data = None

    def get_validation_errors(
        self,
        mmse_data: Dict[str, int | None],
        moca_data: Dict[str, int | None],
        only_one_error: bool = False,
    ) -> Dict[str, str]:
        """
        获取验证错误的详细信息

        Args:
            mmse_data: MMSE评估数据
            moca_data: MoCA评估数据
            only_one_error: 是否只返回一个错误
        Returns:
            字段名到错误信息的映射
        """
        errors = {}

        # 验证MMSE数据
        for item_config in self.mmse_items:
            item_name = item_config["name"]
            max_score = item_config["max_score"]
            score = mmse_data.get(item_name)
            if score is None:
                errors[f"mmse_{item_name}"] = f"{item_name}分值不能为空"
            elif not isinstance(score, int):
                errors[f"mmse_{item_name}"] = f"{item_name}必须是整数"
            elif not (0 <= score <= max_score):
                errors[f"mmse_{item_name}"] = (
                    f"{item_name}分值必须在 0-{max_score} 之间"
                )
            if only_one_error and errors:
                return errors

        # 验证MoCA数据
        for item_config in self.moca_items:
            item_name = item_config["name"]
            max_score = item_config["max_score"]
            score = moca_data.get(item_name)
            if score is None:
                errors[f"moca_{item_name}"] = f"{item_name}分值不能为空"
            elif not isinstance(score, int):
                errors[f"moca_{item_name}"] = f"{item_name}必须是整数"
            elif not (0 <= score <= max_score):
                errors[f"moca_{item_name}"] = (
                    f"{item_name}分值必须在 0-{max_score} 之间"
                )
            if only_one_error and errors:
                return errors

        return errors

    def handle_form_submit(
        self, mmse_data: Dict[str, int | None], moca_data: Dict[str, int | None]
    ) -> None:
        """
        处理表单提交

        Args:
            mmse_data: MMSE评估数据
            moca_data: MoCA评估数据
        """
        # 验证数据
        if not self.validate_assessment_data(mmse_data, moca_data):
            errors = self.get_validation_errors(
                mmse_data, moca_data, only_one_error=True
            )
            error_message = "\n".join(errors.values())
            if self._view:
                self._view.show_error(error_message)
            return

        # 保存数据并通过回调通知完成
        try:
            self.save_assessment_data(mmse_data, moca_data)
            assessment_dict = self.get_assessment_data()

            if self._on_complete_callback and assessment_dict:
                self._on_complete_callback(assessment_dict)

        except Exception as e:
            if self._view:
                self._view.show_error(f"保存失败: {e}")

    def handle_back_clicked(
        self, mmse_data: Dict[str, int | None], moca_data: Dict[str, int | None]
    ) -> None:
        """
        处理返回按钮点击

        Args:
            mmse_data: MMSE评估数据
            moca_data: MoCA评估数据
        """
        # 保存当前数据（无论是否完整）
        try:
            self.save_assessment_data(mmse_data, moca_data)
            assessment_dict = self.get_assessment_data() or {}

            # 通过回调通知返回
            if self._on_back_callback:
                self._on_back_callback(assessment_dict)

        except Exception as e:
            print(f"⚠️ 保存评估数据失败: {e}")
            # 即使保存失败也允许返回，使用空数据
