"""
MVP架构 - 全局应用Presenter
负责整个应用的流程控制和业务逻辑
"""

from typing import Any, Dict, Optional, TYPE_CHECKING, Callable
from datetime import datetime
from pathlib import Path
import json

from .assessment_presenter import AssessmentPresenter
from .home_presenter import HomePresenter
from .experiment_presenter import ExperimentPresenter

if TYPE_CHECKING:
    from ..interfaces.view_interfaces import IMainWindowView


class ApplicationPresenter:
    """
    全局应用Presenter - 管理整个应用的生命周期和业务流程

    职责：
    1. 管理用户信息和评估数据的完整流程
    2. 控制页面切换逻辑
    3. 处理数据持久化
    4. 协调各个子Presenter
    5. 管理实验流程
    """

    def __init__(self, 
                 dry_run: bool = False,
                 et_session=None,
                 paradigm_service=None,
                 skip_calibrate: bool = False):
        """初始化应用Presenter - 包含所有依赖注入"""
        self._view: Optional["IMainWindowView"] = None
        self._home_presenter = None
        self._assessment_presenter = None
        self._experiment_presenter = None
        
        # 配置参数
        self.dry_run = dry_run
        self.skip_calibrate = skip_calibrate
        self.et_session = et_session
        self.paradigm_service = paradigm_service

        # 应用状态
        self._current_page = "home"  # home, assessment
        self._user_info: Optional[Dict[str, Any]] = None
        self._assessment_data: Optional[Dict[str, Any]] = None
        self._session_data: Optional[Dict[str, Any]] = None

        # 回调
        self._on_experiment_complete: Optional[Callable[[Dict[str, Any]], None]] = None

    def set_view(self, view: "IMainWindowView") -> None:
        """设置主窗口View"""
        self._view = view

    def set_sub_presenters(
        self, home_presenter, assessment_presenter, experiment_presenter=None
    ):
        """设置子Presenter"""
        self._home_presenter = home_presenter
        self._assessment_presenter = assessment_presenter
        self._experiment_presenter = experiment_presenter

        # 配置子Presenter的回调
        if self._home_presenter:
            self._home_presenter.set_on_complete_callback(self._on_user_info_complete)

        if self._assessment_presenter:
            self._assessment_presenter.set_on_complete_callback(
                self._on_assessment_complete
            )
            self._assessment_presenter.set_on_back_callback(self._on_assessment_back)

    def start_application(self) -> None:
        """启动应用"""
        print("🚀 应用Presenter启动")
        
        # 设置窗口标题（根据演示模式）
        if self._view:
            title = "眼动仪数据采集系统"
            if self.dry_run:
                title += " (演示模式)"
            self._view.setWindowTitle(title)
        
        # 初始化眼动仪
        if not self.dry_run and self.et_session:
            self.et_session.apply_eyetracker()
        else:
            print("🎯 演示模式：跳过眼动仪初始化")
            
        # 初始化子Presenter
        self._init_presenters()
        
        # 设置页面
        if self._view:
            self._view.set_presenters(self._home_presenter, self._assessment_presenter)
            self._view.playback_window = self._create_playback_window()
            if self._view.playback_window:
                self._view.set_experiment_callbacks(self.on_experiment_complete)
        
        # 启动应用流程
        self._current_page = "home"
        self._restore_session_data()
        self._show_home_page()

    def _init_presenters(self) -> None:
        """初始化所有Presenter"""
        # 创建子Presenter
        self._home_presenter = HomePresenter()
        self._assessment_presenter = AssessmentPresenter()
        
        # 创建实验Presenter（如果服务可用）
        if self.paradigm_service:
            self._experiment_presenter = ExperimentPresenter(
                self.paradigm_service.collector,
                self.paradigm_service
            )
        
        # 配置子Presenter的回调
        self.set_sub_presenters(
            home_presenter=self._home_presenter,
            assessment_presenter=self._assessment_presenter,
            experiment_presenter=self._experiment_presenter
        )

    def _show_home_page(self) -> None:
        """显示用户信息页面"""
        self._current_page = "home"
        if self._view:
            self._view.show_home_page()
            # 恢复之前的数据
            if self._user_info:
                self._view.restore_user_info(self._user_info)

    def _show_assessment_page(self) -> None:
        """显示评估页面"""
        self._current_page = "assessment"
        if self._view:
            self._view.show_assessment_page()
            # 恢复之前的数据
            if self._assessment_data:
                self._view.restore_assessment_data(self._assessment_data)

    def _on_user_info_complete(self, user_info: Dict[str, Any]) -> None:
        """用户信息录入完成"""
        print(f"👤 用户信息完成: {user_info}")
        self._user_info = user_info
        self._save_session_data()
        self._show_assessment_page()

    def _on_assessment_back(self, assessment_data: Dict[str, Any]) -> None:
        """从评估页面返回"""
        print("🔙 从评估页面返回")
        self._assessment_data = assessment_data
        self._save_session_data()
        self._show_home_page()

    def _on_assessment_complete(self, assessment_data: Dict[str, Any]) -> None:
        """评估完成"""
        print(f"📝 评估完成: {assessment_data}")
        self._assessment_data = assessment_data

        # 验证完整数据
        if not self._validate_complete_data():
            return

        # 保存完整记录
        self._save_complete_record()

        # 启动实验
        self._start_experiment()

    def _validate_complete_data(self) -> bool:
        """验证完整数据"""
        if not self._user_info:
            self._view.show_error("缺少用户信息")
            return False

        if not self._assessment_data:
            self._view.show_error("缺少评估数据")
            return False

        # 使用子Presenter验证
        if self._home_presenter and not self._home_presenter.validate_user_info(
            self._user_info
        ):
            errors = self._home_presenter.get_validation_errors(self._user_info)
            self._view.show_error("用户信息不完整:\n" + "\n".join(errors.values()))
            return False

        mmse_data = self._assessment_data.get("mmse", {})
        moca_data = self._assessment_data.get("moca", {})

        if (
            self._assessment_presenter
            and not self._assessment_presenter.validate_assessment_data(
                mmse_data, moca_data
            )
        ):
            errors = self._assessment_presenter.get_validation_errors(
                mmse_data, moca_data
            )
            self._view.show_error("评估数据不完整:\n" + "\n".join(errors.values()))
            return False

        return True

    def _save_complete_record(self) -> None:
        """保存完整记录"""
        try:
            complete_record = {
                "subject_info": self._user_info,
                "assessment_record": self._assessment_data,
                "timestamp": datetime.now().isoformat(),
                "session_id": f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            }

            # 保存到文件
            data_dir = Path("data")
            data_dir.mkdir(exist_ok=True)

            subject_name = self._user_info.get("name", "unknown")
            subject_dir = data_dir / subject_name
            subject_dir.mkdir(exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"subject_{timestamp}.json"
            filepath = subject_dir / filename

            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(complete_record, f, ensure_ascii=False, indent=2)

            print(f"💾 完整记录已保存: {filepath}")

        except Exception as e:
            print(f"❌ 保存记录失败: {e}")
            raise

    def _start_experiment(self) -> None:
        """启动实验 - 包括校准流程"""
        if not self._view:
            return
            
        try:
            session_name = self._generate_session_name()
            print(f"🚀 启动实验: {session_name}")
            print(f"👤 用户信息: {self._user_info}")
            print(f"📝 评估数据: {self._assessment_data}")
            
            # 执行校准流程并启动实验
            self._start_experiment_with_calibration(session_name)
            
        except Exception as e:
            print(f"❌ 启动实验失败: {e}")
            self._view.show_error(f"启动实验失败: {e}")

    def _start_experiment_with_calibration(self, session_name: str) -> None:
        """执行校准并启动实验"""
        try:
            # 执行校准流程
            if self.dry_run or self.skip_calibrate:
                self._skip_calibration(self.skip_calibrate)
            else:
                self._run_calibration()
            
            # 启动实验
            self._start_experiment_playback(session_name)
            
        except Exception as e:
            print(f"❌ 启动实验流程失败: {e}")
            self._view.show_error(f"启动实验失败: {e}")

    def _create_playback_window(self) -> None:
        """创建播放窗口"""
        if not self._experiment_presenter:
            return None
        return PlaybackWindow(self._experiment_presenter)

    def _start_experiment_playback(self, session_name: str) -> None:
        """启动实验播放"""
        if not self._experiment_presenter or not self._view.playback_window:
            print("❌ 实验系统未准备好")
            self._view.show_error("实验系统未准备好，请检查配置")
            return

        self._view.playback_window.start_experiment(
            session_name, self._user_info, self._assessment_data
        )

    def _run_calibration(self) -> None:
        """运行眼动仪校准"""
        try:
            print("🎯 开始校准...")
            if self.et_session is None:
                raise ValueError("眼动仪会话未初始化")
            self.et_session.start_calibration()
            print("✅ 校准成功")
            # 校准完成后，实验会自动开始
        except Exception as e:
            print(f"❌ 校准过程出错: {e}")
            self._view.show_error(f"校准过程出错: {e}")

    def _skip_calibration(self, skip_calibrate: bool = False) -> None:
        """运行演示校准"""
        if skip_calibrate:
            print("🎯 演示模式：跳过校准...")
        else:
            print("🎯 跳过校准，开始播放")

        # 提示用户校准完成
        from PySide6.QtWidgets import QMessageBox
        msg_box = QMessageBox()
        msg_box.setWindowTitle("校准完成")
        msg_box.setText("校准完成，点击确定开始播放")
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.setStyleSheet(
            """
            QMessageBox { background-color: white; color: black; }
            QMessageBox QLabel { background-color: white; color: #333; }
            QMessageBox QPushButton { background-color: #409eff; color: white; border: none; padding: 6px 20px; border-radius: 4px; }
            QMessageBox QPushButton:hover { background-color: #66b1ff; }
            QMessageBox QPushButton:pressed { background-color: #3a8ee6; }
            """
        )
        msg_box.exec()
        print("✅ 校准完成，开始播放")

    def _generate_session_name(self) -> str:
        """生成会话名称"""
        user_name = (
            self._user_info.get("name", "unknown") if self._user_info else "unknown"
        )
        timestamp = datetime.now().strftime("%y%m%d%H%M%S")
        return f"{timestamp}_{user_name}"

    def _save_session_data(self) -> None:
        """保存会话数据"""
        try:
            session_data = {
                "user_info": self._user_info,
                "assessment_data": self._assessment_data,
                "current_page": self._current_page,
            }

            cache_dir = Path("cache")
            cache_dir.mkdir(exist_ok=True)

            with open(cache_dir / "session.json", "w", encoding="utf-8") as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"⚠️ 保存会话数据失败: {e}")

    def _restore_session_data(self) -> None:
        """恢复会话数据"""
        try:
            cache_file = Path("cache") / "session.json"
            if cache_file.exists():
                with open(cache_file, "r", encoding="utf-8") as f:
                    session_data = json.load(f)

                self._user_info = session_data.get("user_info")
                self._assessment_data = session_data.get("assessment_data")
                self._current_page = session_data.get("current_page", "home")

                print("🔄 会话数据已恢复")

        except Exception as e:
            print(f"⚠️ 恢复会话数据失败: {e}")

    def on_experiment_complete(self, results: Dict[str, Any]) -> None:
        """实验完成处理"""
        retry_requested = results.get("retry_requested", False)
        save_data = results.get("save_data", True)

        if retry_requested:
            # 保留数据，重新校准后采集
            print("🔄 需要重新采集，保留表单数据，跳转到校准")
            self._save_session_data()
            # 重新采集时，直接启动实验（包含校准流程）
            if self._view:
                session_name = self._generate_session_name()
                self._view.start_experiment(
                    session_name, self._user_info, self._assessment_data
                )
            return
        else:
            # 根据数据质量决定是否保存
            if save_data:
                print("💾 保存数据，清空表单")
                self._user_info = None
                self._assessment_data = None
                self._clear_session_data()
            else:
                print("⏭️ 跳过保存，清空表单")
                self._user_info = None
                self._assessment_data = None
                self._clear_session_data()

        if self._view:
            self._view.show_home_page()

    def _clear_session_data(self) -> None:
        """清空会话数据"""
        try:
            cache_file = Path("cache") / "session.json"
            if cache_file.exists():
                cache_file.unlink()
        except Exception as e:
            print(f"⚠️ 清空会话数据失败: {e}")

    def get_current_user_info(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        return self._user_info

    def get_current_assessment_data(self) -> Optional[Dict[str, Any]]:
        """获取当前评估数据"""
        return self._assessment_data

    def clear_all_data(self) -> None:
        """清空所有数据"""
        self._user_info = None
        self._assessment_data = None
        self._session_data = None

        if self._home_presenter:
            self._home_presenter.clear_user_info()
        if self._assessment_presenter:
            self._assessment_presenter.clear_assessment_data()

        self._clear_session_data()
