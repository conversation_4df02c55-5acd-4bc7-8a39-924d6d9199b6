"""
数据采集 - 眼动数据收集和存储
合并了原有collections.py和core/data_collector.py的功能
"""

import csv
import threading
import time
from pathlib import Path
from typing import TYPE_CHECKING, Any, Optional

if TYPE_CHECKING:
    from qyem_demo.eyetracker import EyeTracker

try:
    import tobii_research as tr

    TR_AVAILABLE = True
except ImportError:
    tr = None  # type: ignore
    TR_AVAILABLE = False
    print("⚠️ tobii_research 库未安装，将使用模拟模式")


class DataCollector:
    """
    数据采集器

    职责：
    1. 数据采集和存储
    2. 简化的线程安全设计
    """

    def __init__(
        self, eyetracker: Optional["EyeTracker"] = None, is_mock: bool = False
    ):
        """
        初始化数据采集器

        Args:
            eyetracker: 眼动仪实例
            is_mock: 是否为模拟模式
        """
        self._is_mock = is_mock
        self.eyetracker = eyetracker

        # 数据存储
        self._gaze_data_storage: list[dict[str, Any]] = []

        # 简化的状态管理 - 单一锁保护所有状态
        self._lock = threading.Lock()
        self._is_collecting = False
        self._start_time: Optional[float] = None

        # 简化的刺激上下文 - 使用字典而不是多个变量
        self._stimulus_context: dict[str, Any] = {
            "index": None,
            "name": None,
            "path": None,
            "collect": True
        }

        if self._is_mock:
            print("🔧 数据采集器运行在模拟模式")
        else:
            print("📊 数据采集器连接到真实眼动仪")

    def set_current_stimulus(self, index: int, name: Optional[str] = None, path: Optional[str] = None, collect: bool = True) -> None:
        """
        设置当前刺激上下文

        Args:
            index: 刺激索引
            name: 刺激名称
            path: 刺激路径
            collect: 是否采集数据
        """
        with self._lock:
            self._stimulus_context.update({
                "index": index,
                "name": name,
                "path": path,
                "collect": collect
            })

        print(f"📊 设置刺激上下文: 索引={index}, 名称={name}, 采集={'是' if collect else '否'}")

    def clear_current_stimulus(self) -> None:
        """清除刺激上下文"""
        with self._lock:
            self._stimulus_context.update({
                "index": None,
                "name": None,
                "path": None,
                "collect": True
            })



    def start_collection(self) -> bool:
        """开始数据采集"""
        with self._lock:
            if self._is_collecting:
                return False

            self._start_time = time.time()
            self._is_collecting = True

        if self._is_mock:
            print("🔧 模拟模式：开始数据采集")
            return True

        try:
            if self.eyetracker:
                self.eyetracker.subscribe_to(
                    tr.EYETRACKER_GAZE_DATA,
                    self._gaze_data_callback,
                    as_dictionary=True,
                )
            print("📊 开始眼动数据采集")
            return True

        except Exception as e:
            # 回滚状态
            with self._lock:
                self._is_collecting = False
            print(f"❌ 启动采集失败: {e}")
            return False

    def stop_collection(self) -> bool:
        """停止数据采集"""
        with self._lock:
            if not self._is_collecting:
                return False

            self._is_collecting = False
            data_count = len(self._gaze_data_storage)

        if self._is_mock:
            print(f"🔧 模拟模式：停止采集，共 {data_count} 个数据点")
            return True

        try:
            if self.eyetracker:
                self.eyetracker.unsubscribe_from(
                    tr.EYETRACKER_GAZE_DATA, self._gaze_data_callback
                )
            print(f"📊 停止采集，共 {data_count} 个数据点")
            return True

        except Exception as e:
            print(f"❌ 停止采集失败: {e}")
            return False

    def collect_for_duration(
        self,
        duration_s: float,
        stop_event: Optional[threading.Event] = None,
    ) -> list[dict[str, Any]]:
        """
        采集指定时长的数据

        Args:
            duration_s: 采集时长（秒）
            stop_event: 外部停止事件，用于提前终止采集

        Returns:
            采集的数据列表
        """
        if not self.start_collection():
            return []

        try:
            start_time = time.time()
            while time.time() - start_time < duration_s:
                # 检查外部停止事件
                if stop_event and stop_event.is_set():
                    print("⚠️ 收到外部停止信号，提前结束采集")
                    break
                time.sleep(0.05)  # 50ms检查间隔

            return self.get_collected_data()

        finally:
            self.stop_collection()

    def get_collected_data(self) -> list[dict[str, Any]]:
        """获取采集数据的副本"""
        with self._lock:
            return self._gaze_data_storage.copy()

    def clear_data(self) -> None:
        """清空采集数据"""
        with self._lock:
            self._gaze_data_storage.clear()

    def is_collecting(self) -> bool:
        """检查是否正在采集"""
        with self._lock:
            return self._is_collecting

    def get_collection_status(self) -> dict[str, Any]:
        """
        获取采集状态

        Returns:
            采集状态信息
        """
        with self._lock:
            is_collecting = self._is_collecting
            start_time = self._start_time
            data_count = len(self._gaze_data_storage)

        duration = 0.0
        if start_time and is_collecting:
            duration = time.time() - start_time

        return {
            "is_collecting": is_collecting,
            "data_count": data_count,
            "duration": duration,
            "start_time": start_time,
            "mode": "mock" if self._is_mock else "real",
        }

    def save_data(
        self, data: Optional[list[dict[str, Any]]] = None, output_path: str = "data.csv"
    ) -> None:
        """
        保存数据到CSV文件

        Args:
            data: 要保存的数据，None时使用当前采集的数据
            output_path: 输出文件路径

        Returns:
            是否保存成功
        """
        if data is None:
            data = self.get_collected_data()

        if not data:
            print("⚠️ 没有数据可保存")
            return

        # 确保输出目录存在
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # 保存为CSV文件
        with open(output_file, "w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=data[0].keys())
            writer.writeheader()
            writer.writerows(data)

        print(f"✅ 数据已保存到: {output_path}")
        print(f"   共保存 {len(data)} 个数据点")

    def _gaze_data_callback(self, gaze_data: dict[str, Any]) -> None:
        """
        简化的眼动数据回调函数
        使用非阻塞锁避免死锁
        """
        # 非阻塞获取锁，避免死锁
        if not self._lock.acquire(blocking=False):
            return

        try:
            # 快速检查状态
            if not self._is_collecting:
                return

            # 获取刺激上下文
            stimulus_ctx = self._stimulus_context.copy()
            start_time = self._start_time

            # 检查是否需要采集
            if not stimulus_ctx.get("collect", True):
                return

            # 构建数据点
            data_point = dict(gaze_data)
            data_point["collection_timestamp"] = time.time()

            if start_time:
                data_point["relative_timestamp"] = time.time() - start_time

            # 添加刺激上下文
            data_point.update({
                "stimulus_index": stimulus_ctx.get("index"),
                "stimulus_name": stimulus_ctx.get("name"),
                "stimulus_path": stimulus_ctx.get("path"),
                "stimulus_collect_flag": stimulus_ctx.get("collect", True)
            })

            # 保存数据
            self._gaze_data_storage.append(data_point)

        finally:
            self._lock.release()

    def get_data_summary(self) -> dict[str, Any]:
        """
        获取数据摘要信息

        Returns:
            数据摘要
        """
        data = self.get_collected_data()
        if not data:
            return {"count": 0, "duration": 0}

        summary = {
            "count": len(data),
            "first_timestamp": data[0].get("collection_timestamp", 0),
            "last_timestamp": data[-1].get("collection_timestamp", 0),
            "duration": data[-1].get("relative_timestamp", 0)
            if "relative_timestamp" in data[-1]
            else 0,
            "mode": "mock" if self._is_mock else "real",
        }

        return summary

    def check_data_quality(self) -> dict[str, Any]:
        """
        检查采集数据的质量
        
        检查左右眼数据有效性，计算无效数据比例
        
        Returns:
            数据质量检查结果
        """
        data = self.get_collected_data()
        if not data:
            return {
                "valid": False,
                "total_count": 0,
                "left_invalid_ratio": 0.0,
                "right_invalid_ratio": 0.0,
                "left_valid_count": 0,
                "right_valid_count": 0,
                "message": "没有采集到数据"
            }

        total_count = len(data)
        left_valid_count = 0
        right_valid_count = 0
        
        # 统计左右眼有效数据
        for point in data:
            # 检查左眼有效性
            left_validity = point.get("left_gaze_point_validity", 0)
            if left_validity == 1:
                left_valid_count += 1
                
            # 检查右眼有效性
            right_validity = point.get("right_gaze_point_validity", 0)
            if right_validity == 1:
                right_valid_count += 1

        # 计算无效数据比例
        left_invalid_count = total_count - left_valid_count
        right_invalid_count = total_count - right_valid_count
        
        left_invalid_ratio = left_invalid_count / total_count if total_count > 0 else 0.0
        right_invalid_ratio = right_invalid_count / total_count if total_count > 0 else 0.0

        # 判断数据质量
        is_quality_good = left_invalid_ratio < 0.3 and right_invalid_ratio < 0.3
        
        return {
            "valid": is_quality_good,
            "total_count": total_count,
            "left_invalid_ratio": left_invalid_ratio,
            "right_invalid_ratio": right_invalid_ratio,
            "left_valid_count": left_valid_count,
            "right_valid_count": right_valid_count,
            "left_invalid_count": left_invalid_count,
            "right_invalid_count": right_invalid_count,
            "message": "数据质量良好" if is_quality_good else "数据质量不合格"
        }
