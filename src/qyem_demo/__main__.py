"""
qyem-demo - 眼动仪数据采集演示程序主入口

这是一个用于演示眼动仪数据采集功能的程序，支持以下功能：
1. 眼动仪设备连接和校准
2. 自定义范式播放和数据采集
3. 数据导出和分析

使用方法:
    python -m qyem_demo --help              # 显示帮助信息
    python -m qyem_demo --config config.toml  # 使用指定配置文件
    python -m qyem_demo --dry-run           # 启动演示模式（无硬件）
"""

import argparse
import sys

from qyem_demo.eyetracker import EyeTrackerSession

from .gui_app import GuiApp
from .__about__ import __version__, __summary__


def create_main_parser() -> argparse.ArgumentParser:
    """
    创建主要的命令行参数解析器

    Returns:
        参数解析器
    """
    parser = argparse.ArgumentParser(
        description=f"{__summary__} (Version {__version__})",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用说明:
  启动图形界面进行眼动仪数据采集

示例用法:
  # 基本使用
  python -m qyem_demo

  # 使用自定义配置
  python -m qyem_demo --config config.toml

  # 演示模式（不连接真实眼动仪）
  python -m qyem_demo --dry-run

  # 应用许可证
  python -m qyem_demo --license license.bytes --config config.toml
        """,
    )

    parser.add_argument(
        "-v", "--version",
        action="version",
        version=f"%(prog)s {__version__}"
    )

    parser.add_argument(
        "--list-trackers",
        action="store_true",
        help="列出所有可用的眼动仪设备并退出"
    )

    parser.add_argument(
        "--skip-calibrate",
        action="store_true",
        help="跳过眼动仪校准"
    )

    parser.add_argument(
        "--config",
        "-c",
        type=str,
        default="config.toml",
        help="配置文件路径 (默认: config.toml)",
    )

    parser.add_argument("--license", "-l", type=str, help="许可证文件路径")

    parser.add_argument(
        "--dry-run", action="store_true", help="演示模式（不连接真实眼动仪）"
    )

    return parser


def main() -> int:
    """
    主函数

    Returns:
        退出代码（0表示成功）
    """
    # 解析命令行参数
    parser = create_main_parser()
    args = parser.parse_args()

    try:
        # 处理特殊参数
        if args.list_trackers:
            print("📋 列出所有可用的眼动仪设备...")
            try:
                devices = EyeTrackerSession.find_devices()
                if not devices:
                    print("❌ 未找到任何眼动仪设备")
                    return 1
                else:
                    print(f"✅ 共找到 {len(devices)} 个眼动仪设备")
                    return 0
            except Exception as e:
                print(f"❌ 列出眼动仪设备时发生错误: {e}")
                return 1
            
        # 打印系统信息
        print("🚀 眼动仪数据采集系统")
        print("=" * 50)
        print("模式: GUI")
        if args.config:
            print(f"配置文件: {args.config}")
        print(f"演示模式: {'是' if args.dry_run else '否'}")
        if args.license:
            print(f"许可证: {args.license}")
        print("=" * 50)

        # 启动GUI应用
        gui_app = GuiApp(dry_run=args.dry_run)
        return gui_app.run(args)

    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
        return 1
    except Exception as e:
        print(f"\n💥 程序发生未预期错误: {e}")
        import traceback

        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
