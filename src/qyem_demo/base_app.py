"""
基础应用类 - 提供通用的应用功能和生命周期管理
"""

import argparse
from pathlib import Path
import sys

from .collector import DataCollector
from .config import Config
from .eyetracker import EyeTrackerSession
from .paradigm import ParadigmService


class BaseApp:
    """基础应用类 - 提供通用功能"""

    def __init__(self, dry_run: bool = False):
        """
        初始化基础应用

        Args:
            dry_run: 是否为演示模式
        """
        self.dry_run = dry_run

        # 核心组件
        self.config: Config | None = None
        self.et_session: EyeTrackerSession | None = None
        self.collector: DataCollector | None = None
        self.paradigm_service: ParadigmService | None = None

        print(f"🔧 基础应用初始化 (演示模式: {'是' if dry_run else '否'})")

    def _load_config(self, config_path: str) -> bool:
        """
        加载配置文件

        Args:
            config_path: 配置文件路径

        Returns:
            是否加载成功
        """
        try:
            print(f"📋 加载配置文件: {config_path}")
            self.config = Config(config_path)
            config_data = self.config.load_config()
            
            if config_data is None:
                print("❌ 配置文件加载失败")
                return False
                
            print("✅ 配置文件加载成功")
            return True
            
        except Exception as e:
            print(f"❌ 配置文件加载异常: {e}")
            return False

    def _connect_eyetracker(self) -> bool:
        """
        连接眼动仪设备

        Returns:
            是否连接成功
        """
        if self.dry_run:
            print("🎯 演示模式：跳过眼动仪连接")
            return True

        try:
            print("🔌 连接眼动仪...")
            self.et_session = EyeTrackerSession()

            # 连接第一个设备
            if not self.et_session.apply_eyetracker():
                print("❌ 眼动仪连接失败")
                return False

            print("✅ 眼动仪连接成功")
            return True
            
        except Exception as e:
            print(f"❌ 眼动仪连接异常: {e}")
            return False

    def _auto_apply_license(self) -> bool:
        """
        自动应用许可证（如果找到）

        Returns:
            是否应用成功
        """
        if self.dry_run:
            print("🎯 演示模式：跳过自动许可证应用")
            return True

        try:
            # 查找licenses目录
            if getattr(sys, 'frozen', False):
                base_path = Path(getattr(sys, '_MEIPASS', Path(__file__).parent.parent.parent))
            else:
                base_path = Path(__file__).parent.parent.parent
            
            licenses_dir = base_path / 'licenses'
            if not licenses_dir.exists():
                print("📁 未找到licenses目录，跳过自动许可证应用")
                return True
            
            # 查找.bytes许可证文件
            license_files = [f for f in licenses_dir.iterdir() if f.name.endswith(".bytes")]
            if not license_files:
                print("📁 licenses目录中未找到.bytes许可证文件")
                return True

            # 应用第一个找到的许可证文件
            license_file = license_files[0]
            license_path = licenses_dir / license_file
            print(f"🔍 自动发现许可证文件: {license_path}")

            return self._apply_license(str(license_path))

        except Exception as e:
            print(f"❌ 自动许可证应用异常: {e}")
            return False

    def _apply_license(self, license_path: str) -> bool:
        """
        应用许可证文件

        Args:
            license_path: 许可证文件路径

        Returns:
            是否应用成功
        """
        if self.dry_run:
            print("🎯 演示模式：跳过许可证应用")
            return True

        try:
            print(f"📜 应用许可证: {license_path}")
            
            if not self.et_session:
                print("❌ 眼动仪会话未初始化")
                return False

            if self.et_session.apply_license(license_path):
                print("✅ 许可证应用成功")
                return True
            else:
                print("❌ 许可证应用失败")
                return False
                
        except Exception as e:
            print(f"❌ 许可证应用异常: {e}")
            return False

    def _run_calibration(self) -> bool:
        """
        运行眼动仪校准

        Returns:
            是否校准成功
        """
        if self.dry_run:
            print("🎯 演示模式：跳过眼动仪校准")
            return True

        try:
            print("🎯 开始校准...")
            
            if not self.et_session:
                print("❌ 眼动仪会话未初始化")
                return False

            if self.et_session.start_calibration():
                print("✅ 校准成功")
                return True
            else:
                print("❌ 校准失败")
                return False
                
        except Exception as e:
            print(f"❌ 校准异常: {e}")
            return False

    def _initialize_core_components(self) -> None:
        """
        初始化核心组件
        """
        print("🔧 初始化核心组件...")

        # 1. 初始化数据采集器
        self.collector = DataCollector(
            eyetracker=self.et_session.eyetracker if self.et_session else None,
            is_mock=self.dry_run,
        )

        # 2. 初始化范式服务
        if self.config and self.collector:
            self.paradigm_service = ParadigmService(
                config=self.config, collector=self.collector
            )

        print("✅ 核心组件初始化完成")

    def _cleanup(self) -> None:
        """清理资源"""
        print("🧹 清理应用资源...")

        # 停止数据采集
        if self.collector:
            try:
                self.collector.stop_collection()
            except Exception as e:
                print(f"⚠️ 数据采集器清理异常: {e}")

        # 断开眼动仪连接
        if self.et_session:
            try:
                self.et_session.clear_eyetracker()
            except Exception as e:
                print(f"⚠️ 眼动仪断开异常: {e}")

        print("✅ 资源清理完成")

    def run(self, args: argparse.Namespace) -> int:
        """
        运行应用 - 子类需要重写此方法

        Args:
            args: 命令行参数

        Returns:
            退出代码
        """
        raise NotImplementedError("子类必须实现 run 方法") 