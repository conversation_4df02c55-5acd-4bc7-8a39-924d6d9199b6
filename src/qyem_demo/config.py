"""
配置管理 - 统一配置加载、验证和管理
"""

from pathlib import Path
from typing import Any, cast

import toml


class Config:
    """配置管理器 - 统一的配置管理实现"""

    def __init__(self, config_path: str | None = None):
        """
        初始化配置管理器

        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        self._config_data: dict[str, Any] | None = None
        self._config_path: str | None = None

        if config_path is None:
            # 默认路径指向项目根目录的config.toml
            default_path = Path(__file__).parent.parent.parent / "config.toml"
            self._config_path = str(default_path)
        else:
            self._config_path = config_path

    def load_config(self, config_path: str | None = None) -> dict[str, Any] | None:
        """
        加载配置文件

        Args:
            config_path: 配置文件路径，None时使用实例的默认路径

        Returns:
            配置数据字典，失败时返回None
        """
        use_path = config_path or self._config_path
        if not use_path:
            print("❌ 未指定配置文件路径")
            return None

        try:
            config_file = Path(use_path)
            if not config_file.exists():
                print(f"❌ 配置文件不存在: {use_path}")
                return None

            with open(config_file, encoding="utf-8") as f:
                self._config_data = toml.load(f)
                self._config_path = use_path

            print(f"✅ 配置文件加载成功: {use_path}")
            return self._config_data

        except toml.TomlDecodeError as e:
            print(f"❌ 配置文件格式无效 {use_path}: {e}")
            return None
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            return None

    def save_config(
        self, config_data: dict[str, Any] | None = None, output_path: str | None = None
    ) -> bool:
        """
        保存配置到TOML文件

        Args:
            config_data: 配置字典，None时使用当前配置
            output_path: 输出文件路径，None时使用当前路径

        Returns:
            是否成功保存
        """
        data_to_save = config_data or self._config_data
        if not data_to_save:
            print("❌ 没有配置数据可保存")
            return False

        save_path = output_path or self._config_path
        if not save_path:
            print("❌ 没有指定保存路径")
            return False

        try:
            with open(save_path, "w", encoding="utf-8") as f:
                toml.dump(data_to_save, f)
            print(f"✅ 配置已保存到: {save_path}")
            return True
        except Exception as e:
            print(f"❌ 保存配置文件时发生错误: {e}")
            return False

    def get_config(self) -> dict[str, Any] | None:
        """
        获取当前配置数据

        Returns:
            配置数据字典
        """
        return self._config_data

    def get_section(self, section_name: str) -> dict[str, Any]:
        """
        获取配置的某个部分

        Args:
            section_name: 配置节名称

        Returns:
            配置节数据
        """
        if not self._config_data:
            return {}
        return cast(dict[str, Any], self._config_data.get(section_name, {}))

    def get_value(
        self,
        key_path: str,
        default: Any = None,
        config_data: dict[str, Any] | None = None,
    ) -> Any:
        """
        获取配置值，支持嵌套路径

        Args:
            key_path: 键路径，支持点号分隔，如 "device.name"
            default: 默认值
            config_data: 配置数据，None时使用当前配置

        Returns:
            配置值或默认值
        """
        data = config_data or self._config_data
        if not data:
            return default

        keys = key_path.split(".")
        current = data

        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default

        return current

    def validate_config(self, config_data: dict[str, Any] | None = None) -> list[str]:
        """
        验证配置的有效性

        Args:
            config_data: 要验证的配置字典，None时使用当前配置

        Returns:
            错误信息列表，如果为空则表示配置有效
        """
        data = config_data or self._config_data
        if not data:
            return ["配置数据为空"]

        errors = []

        # 验证基本配置项
        if "description" in data and not isinstance(data["description"], str):
            errors.append("description 必须是字符串")

        if "notes" in data and not isinstance(data["notes"], str):
            errors.append("notes 必须是字符串")

        # 验证设备配置
        if "device" in data:
            device_config = data["device"]
            if not isinstance(device_config, dict):
                errors.append("device 必须是对象")
            else:
                if "name" in device_config and not isinstance(
                    device_config["name"], str
                ):
                    errors.append("device.name 必须是字符串")
                if "eyetracker_address" in device_config and not isinstance(
                    device_config["eyetracker_address"], list
                ):
                    errors.append("device.eyetracker_address 必须是数组")
                if "license_file" in device_config and not isinstance(
                    device_config["license_file"], str
                ):
                    errors.append("device.license_file 必须是字符串")

        # 验证范式配置
        paradigm_section = data.get("collection-paradigm", None)
        if paradigm_section is None:
            errors.append("缺少 collection-paradigm 配置")
        elif not isinstance(paradigm_section, dict):
            errors.append("collection-paradigm 必须是对象")
        else:
            # 验证stimulis_group配置
            stimulis_group = paradigm_section.get("stimulis_group", "group-default")
            if not isinstance(stimulis_group, str):
                errors.append("collection-paradigm.stimulis_group 必须是字符串")
            else:
                # 验证stimulis_group下的配置
                group_config = paradigm_section.get(stimulis_group, None)
                if group_config is None:
                    errors.append(f"缺少 collection-paradigm.{stimulis_group} 配置")
                elif not isinstance(group_config, dict):
                    errors.append(f"collection-paradigm.{stimulis_group} 必须是对象")
                else:
                    # 验证stimulus_dir
                    stimulus_dir = group_config.get("stimulus_dir", "")
                    if stimulus_dir and not isinstance(stimulus_dir, str):
                        errors.append(
                            f"collection-paradigm.{stimulis_group}.stimulus_dir 必须是字符串"
                        )
                    # 验证stimulis
                    stimulis = group_config.get("stimulis", None)
                    if stimulis is None:
                        errors.append(
                            f"collection-paradigm.{stimulis_group}.stimulis 必须配置"
                        )
                    elif not isinstance(stimulis, list):
                        errors.append(
                            f"collection-paradigm.{stimulis_group}.stimulis 必须是数组"
                        )
                    else:
                        for i, s in enumerate(stimulis):
                            if not (isinstance(s, str) or isinstance(s, dict)):
                                errors.append(
                                    f"collection-paradigm.{stimulis_group}.stimulis[{i}] 必须是字符串或对象"
                                )

        # 验证眼动仪配置
        if "tracker_config" in data:
            tracker_config = data["tracker_config"]
            if not isinstance(tracker_config, dict):
                errors.append("tracker_config 必须是对象")
            else:
                if "gaze_output_frequency" in tracker_config:
                    try:
                        freq = float(tracker_config["gaze_output_frequency"])
                        if freq <= 0:
                            errors.append(
                                "tracker_config.gaze_output_frequency 必须大于0"
                            )
                    except (ValueError, TypeError):
                        errors.append(
                            "tracker_config.gaze_output_frequency 必须是有效的数字"
                        )

                if "supported_frequencies" in tracker_config:
                    if not isinstance(tracker_config["supported_frequencies"], list):
                        errors.append("tracker_config.supported_frequencies 必须是数组")
                    else:
                        for i, freq in enumerate(
                            tracker_config["supported_frequencies"]
                        ):
                            try:
                                float_freq = float(freq)
                                if float_freq <= 0:
                                    errors.append(
                                        f"tracker_config.supported_frequencies[{i}] 必须大于0"
                                    )
                            except (ValueError, TypeError):
                                errors.append(
                                    f"tracker_config.supported_frequencies[{i}] 必须是有效的数字"
                                )

        return errors

    def merge_configs(
        self, base_config: dict[str, Any], override_config: dict[str, Any]
    ) -> dict[str, Any]:
        """
        合并两个配置，override_config会覆盖base_config中的值

        Args:
            base_config: 基础配置
            override_config: 覆盖配置

        Returns:
            合并后的配置
        """
        merged = base_config.copy()

        for key, value in override_config.items():
            if (
                key in merged
                and isinstance(merged[key], dict)
                and isinstance(value, dict)
            ):
                merged[key] = self.merge_configs(merged[key], value)
            else:
                merged[key] = value

        return merged

    def create_default_config(self) -> dict[str, Any]:
        """
        创建默认配置模板

        Returns:
            默认配置字典
        """
        return {
            "description": "眼动仪配置示例文件",
            "notes": "此文件包含眼动仪的各种配置参数",
            "global": {"output_dir": "./out"},
            "device": {"name": "", "eyetracker_address": [], "license_file": ""},
            "collection-paradigm": {
                "subscriptions": ["gaze_data"],
                "stimulis_group": "group-default",
            },
            "collection-paradigm.group-default": {
                "stimulus_dir": "./images/group-default",
                "stimulis": [
                    "image, 12s, em_check_calibration.png",
                    "image, 6s, em_empty_table.jpg",
                    "image, 6s, em_photoframe_on_table.jpg",
                ],
            },
            "tracker_config": {
                "gaze_output_frequency": 60.0,
                "supported_frequencies": [30.0, 60.0, 120.0, 250.0],
            },
        }

    # 便捷方法 - 与core/config.py兼容

    def get_output_dir(self) -> str:
        """获取输出目录"""
        return cast(str, self.get_value("global.output_dir", "out"))

    def get_subscriptions(self) -> list[str]:
        """获取数据订阅列表"""
        return cast(
            list[str],
            self.get_value("collection-paradigm.subscriptions", ["gaze_data"]),
        )

    def get_stimuli_group(self) -> str:
        """获取刺激组名称"""
        return cast(
            str, self.get_value("collection-paradigm.stimulis_group", "group-default")
        )

    def get_stimuli_config(self) -> dict[str, Any]:
        """获取刺激配置"""
        stimuli_group = self.get_stimuli_group()
        return cast(
            dict[str, Any], self.get_value(f"collection-paradigm.{stimuli_group}", {})
        )

    def get_device_config(self) -> dict[str, Any]:
        """获取设备配置"""
        return cast(dict[str, Any], self.get_value("device", {}))

    def get_tracker_config(self) -> dict[str, Any]:
        """获取眼动仪配置"""
        return cast(dict[str, Any], self.get_value("tracker_config", {}))


# 为了兼容性，保留ConfigManager别名
ConfigManager = Config
